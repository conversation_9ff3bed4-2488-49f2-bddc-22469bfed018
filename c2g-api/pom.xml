<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chaos</groupId>
        <artifactId>keep-alive-con</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>
    <version>1.0.0.2-SNAPSHOT</version>
    <groupId>com.chaos.im.c2g</groupId>
    <artifactId>c2g-api</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>keep-alive-common-core</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm-commons</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cglib</groupId>
                    <artifactId>cglib-nodep</artifactId>
                </exclusion>
                  <exclusion>
                      <groupId>com.alibaba</groupId>
                      <artifactId>fastjson</artifactId>
                  </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>

                <exclusion>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>


                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-framework</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.chaos</groupId>
                    <artifactId>keep-alive-common-protobuf</artifactId>
                </exclusion>

<!--                <exclusion>-->
<!--                    <groupId>com.outstanding</groupId>-->
<!--                    <artifactId>framework-base</artifactId>-->
<!--                </exclusion>-->

                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-recipes</artifactId>
                </exclusion>

<!--                <exclusion>-->
<!--                    <groupId>com.chaos</groupId>-->
<!--                    <artifactId>basic-common-api</artifactId>-->
<!--                </exclusion>-->
            </exclusions>
        </dependency>
    </dependencies>

</project>