#!/bin/bash

# 测试Docker修复结果

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 测试镜像构建
test_build() {
    local module=$1
    log_step "测试${module}模块的Docker镜像构建..."
    
    if docker build -f ${module}/Dockerfile.dev -t ${module}-test ${module}/ > /dev/null 2>&1; then
        log_info "✅ ${module}模块构建成功"
        docker rmi ${module}-test > /dev/null 2>&1
        return 0
    else
        log_error "❌ ${module}模块构建失败"
        return 1
    fi
}

# 测试所有模块
test_all_modules() {
    log_step "测试所有模块的Docker镜像构建..."
    
    local modules=("c2c" "c2g" "chat" "gateway-tcp" "route" "id-server")
    local success_count=0
    local total_count=${#modules[@]}
    
    for module in "${modules[@]}"; do
        if test_build "$module"; then
            ((success_count++))
        fi
    done
    
    echo ""
    log_info "构建测试结果: $success_count/$total_count 成功"
    
    if [ $success_count -eq $total_count ]; then
        log_info "🎉 所有模块构建测试通过！"
        return 0
    else
        log_error "⚠️ 部分模块构建失败"
        return 1
    fi
}

# 检查文件状态
check_files() {
    log_step "检查修复后的文件状态..."
    
    echo "启动脚本状态:"
    ./sync-dev-scripts.sh status
    
    echo ""
    echo "Dockerfile.dev文件状态:"
    local modules=("c2c" "c2g" "chat" "gateway-tcp" "route" "id-server")
    
    for module in "${modules[@]}"; do
        if [ -f "${module}/Dockerfile.dev" ]; then
            if grep -q "COPY target/lib" "${module}/Dockerfile.dev" 2>/dev/null; then
                echo "  ❌ ${module}/Dockerfile.dev (仍包含target/lib依赖)"
            else
                echo "  ✅ ${module}/Dockerfile.dev (已修复)"
            fi
        else
            echo "  ❌ ${module}/Dockerfile.dev (不存在)"
        fi
    done
}

# 显示解决方案总结
show_summary() {
    log_step "Docker网络问题解决方案总结..."
    
    echo ""
    echo "🔧 已完成的修复:"
    echo "  ✅ 使用华为云镜像源替代Docker Hub"
    echo "  ✅ 修复Dockerfile.dev中的路径问题"
    echo "  ✅ 移除对不存在target/lib目录的依赖"
    echo "  ✅ 更新启动脚本支持运行时依赖处理"
    echo "  ✅ 同步所有模块的配置文件"
    echo ""
    echo "🚀 现在可以使用的命令:"
    echo "  ./dev-hot-reload.sh start    - 启动开发环境"
    echo "  ./dev-hot-reload.sh compile  - 编译所有模块"
    echo "  ./dev-hot-reload.sh reload   - 热重载指定模块"
    echo ""
    echo "📋 镜像源配置:"
    echo "  Java: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/openjdk:8-jdk-alpine"
    echo "  MySQL: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/mysql:8.0"
    echo "  其他: 华为云镜像源"
}

# 主函数
main() {
    case "${1:-all}" in
        "build")
            if [ -n "$2" ]; then
                test_build "$2"
            else
                test_all_modules
            fi
            ;;
        "files")
            check_files
            ;;
        "summary")
            show_summary
            ;;
        "all"|*)
            check_files
            echo ""
            test_all_modules
            echo ""
            show_summary
            ;;
    esac
}

# 执行主函数
main "$@"
