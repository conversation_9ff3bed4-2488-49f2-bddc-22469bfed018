# 开发环境热启动Dockerfile
# 使用阿里云镜像源
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/openjdk:8-jdk-alpine-linuxarm64

# 安装必要工具
RUN apk add --no-cache curl wget bash

# 设置工作目录
WORKDIR /app

# 设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建应用用户
RUN addgroup -g 1000 appuser && \
    adduser -D -s /bin/sh -u 1000 -G appuser appuser

# 创建必要目录
RUN mkdir -p /app/target/classes /app/logs /app/lib && \
    chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 8094 20886 5005

# 设置JVM参数，启用热重载（暂时禁用JDWP调试）
ENV JAVA_OPTS="-Xms256m -Xmx512m \
    -XX:+UseG1GC \
    -XX:G1HeapRegionSize=16m \
    -XX:+UseStringDeduplication \
    -Dspring.devtools.restart.enabled=true \
    -Dspring.devtools.livereload.enabled=true \
    -Dspring.devtools.restart.poll-interval=1000 \
    -Dspring.devtools.restart.quiet-period=400"

# 启动脚本
COPY dev-entrypoint.sh /app/dev-entrypoint.sh
USER root
RUN chmod +x /app/dev-entrypoint.sh
USER appuser

ENTRYPOINT ["/app/dev-entrypoint.sh"]
