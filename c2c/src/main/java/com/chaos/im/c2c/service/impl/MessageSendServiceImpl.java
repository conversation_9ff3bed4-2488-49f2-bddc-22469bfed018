package com.chaos.im.c2c.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;

import com.chaos.im.c2c.api.domain.MessageSendReq;
import com.chaos.im.c2c.domain.C2cMessageAckDO;
import com.chaos.im.c2c.domain.C2cMessageBO;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.web.util.PageUtil;
import com.outstanding.framework.core.PageInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MessageSendServiceImpl extends AbstractMessageSendService {

    private static final Integer MESSAGE_FETCH_THRESHOLD = 10;

    private static final int FACTOR = 128;

    private final MongoTemplate mongoTemplate;

    private static final String ACK_KEY_PREFIX = "IM::C2C_MESSAGE_ACK";

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private RedisTemplate<String, Long> redisTemplate;
    @Autowired
    private RedissonClient redissonClient;


    @Value("#{'topic-c2g-message-send-'.concat('${spring.profiles.active}')}")
    private String TOPIC_C2G_MESSAGE_SEND;

    @Value("#{'topic-c2c-message-send-'.concat('${spring.profiles.active}')}")
    private String TOPIC_C2C_MESSAGE_SEND;

//    @Autowired
//    private RedissonClient redissonClient;

    @Override
    public void save(List<MessageSendJsonRequest> requests, String toOperatorNo) {

        log.info("接收到kafka单聊消息,准备落库,toOperatorNo:{} , req:{}", toOperatorNo, JSONUtil.toJsonStr(requests));

        for (MessageSendJsonRequest request : requests) {
            String lastMessageKey = ImConstants.C2C_LAST_MESSAGE_PREFIX + request.getChatId() + ImConstants.SEPARATOR + request.getToOperatorNo();
            String unReadCountKey =ImConstants.C2C_UNREAD_COUNT_PREFIX + request.getChatId() + ImConstants.SEPARATOR + request.getToOperatorNo();
            List<C2cMessageBO> existMessages = mongoTemplate.find(
                    Query.query(Criteria.where("messageId").is(request.getChatId() + "-" + request.getMessageId())),
                    C2cMessageBO.class);

            if (!CollectionUtil.isEmpty(existMessages)) {
                sendMessageSendResponse(request);
                continue;
            }

            Long chatId = request.getChatId();
            Optional<Long> sequenceOptional = Optional
                    .ofNullable(redisTemplate.boundValueOps(Constants.REDIS_SEQ_KEY + "::" + chatId).increment());
            // 服务端设置sequence
            request.setSequence(sequenceOptional.orElse(1L));
            C2cMessageBO c2cMessageBO = new C2cMessageBO();
            c2cMessageBO.setId(request.getMessageId());
            c2cMessageBO.setCategory(request.getCategory());
            c2cMessageBO.setAppId(request.getToAppId());
            c2cMessageBO.setFromOperatorNo(request.getFromOperatorNo());
            c2cMessageBO.setToOperatorNo(request.getToOperatorNo());
            c2cMessageBO.setContent(request.getContent());
            c2cMessageBO.setChatId(request.getChatId());
            c2cMessageBO.setMessageId(request.getChatId() + "-" + request.getMessageId());
            c2cMessageBO.setTimestamp(System.currentTimeMillis());
            c2cMessageBO.setSequence(request.getSequence());
//
            Map<String,Object> map =  BeanUtil.beanToMap(c2cMessageBO);
            log.info("记录最后一条c2cMessageBo:{}",JSONUtil.toJsonStr(map));
            if(map!=null) {
                Map<String, Object> filteredMap = map.entrySet().stream()
                        .filter(entry -> entry.getValue() != null)
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue
                        ));
                redissonClient.getMap(lastMessageKey).putAll(filteredMap);
            }

            redissonClient.getAtomicLong(unReadCountKey).incrementAndGet();

            mongoTemplate.save(c2cMessageBO);

            sendMessageSendResponse(request);
            sendMessagePush(request);
        }

    }

    @Override
    public PageInfoDTO<MessageJsonPush> fetch(Long chatId, String operatorNo, String deviceId, Long startMessageId,
                                              Long stopMessageId, int size, int current, Map<String, String> sort) {

        Criteria criteria = Criteria.where("chatId").is(chatId);

        if (startMessageId != null && stopMessageId != null) {
            criteria.and("_id").gt(startMessageId).lte(stopMessageId);
        } else if (startMessageId != null && stopMessageId == null) {
            criteria.and("_id").gt(startMessageId);
        } else if (startMessageId == null && stopMessageId != null) {
            criteria.and("_id").lte(stopMessageId);
        }

        // 构建查询对象
        Query query = new Query(criteria);
        if (Objects.nonNull(sort)) {
            sort.entrySet().forEach(entry -> {
                if ("DESC".equals(entry.getValue())) {
                    query.with(Sort.by(Sort.Direction.DESC, entry.getKey()));
                } else {
                    query.with(Sort.by(Sort.Direction.ASC, entry.getKey()));
                }
            });
        }

        // 分页处理
        Pageable page = PageRequest.of(current - 1, size); // 假设从第一页开始
        query.with(page);

        // 执行查询
        List<C2cMessageBO> messageBOList = mongoTemplate.find(query, C2cMessageBO.class);

        long totalCount = mongoTemplate.count(new Query(criteria), C2cMessageBO.class);

        // 将 C2gMessageBO 转换为 MessageJsonPush
        List<MessageJsonPush> messageJsonPushList = messageBOList.stream()
                .map(bo -> convertToMessageJsonPush(bo, operatorNo))
                .collect(Collectors.toList());

        return PageUtil.createPage(current, size, totalCount, messageJsonPushList);

    }

    @Override
    public C2cMessageAckDO lastAck(Long chatId, String operatorNo, String deviceId) {

        return c2cMessageAckDAO.getLastAck(chatId, operatorNo, deviceId);
    }

    @Override
    public Long lastAckMessageId(Long chatId, String operatorNo, String deviceId) {

        return c2cMessageAckDAO.getLastAckMessageId(chatId, operatorNo, deviceId);
    }

    private MessageJsonPush convertToMessageJsonPush(C2cMessageBO c2gMessageBO, String memberId) {
        MessageJsonPush messageJsonPush = new MessageJsonPush();
        messageJsonPush.setMessageId(c2gMessageBO.getId());
        messageJsonPush.setCategory(c2gMessageBO.getCategory());
        messageJsonPush.setFromOperatorNo(c2gMessageBO.getFromOperatorNo());
        messageJsonPush.setContent(c2gMessageBO.getContent());
        messageJsonPush.setChatId(c2gMessageBO.getChatId());
        messageJsonPush.setToOperatorNo(memberId);
        messageJsonPush.setChatType(Constants.CHAT_TYPE_C2C);
        messageJsonPush.setTimestamp(c2gMessageBO.getTimestamp());
        messageJsonPush.setSequence(c2gMessageBO.getSequence());
        return messageJsonPush;
    }

    private String getRowKey(Long toId, Long messageId) {
        Long tempMessageId = messageId;
        if (Objects.isNull(tempMessageId)) {
            tempMessageId = 0L;
        }
        // 逆序消息id，做排序用，最新的消息-先查找
        return getRowKey(toId)
                + StringUtils.leftPad(String.valueOf(Long.MAX_VALUE - tempMessageId), 19, '0');
    }

    private String getRowKey(Long toId) {
        int hash = (int) (toId % FACTOR);
        // 3位会话id的hash+群id+逆序消息id
        // hash，是为了在hbase中分区存储，相当于mysql的分表
        // 群id，rowKey做群消息查询
        return StringUtils.leftPad(String.valueOf(hash), 3, '0') + "|"
                + StringUtils.leftPad(String.valueOf(toId), 19, '0') + "|";
    }

    @Override
    public void sendMessage(MessageSendReq req) {
        MessageSendJsonRequest request = new MessageSendJsonRequest();
        request.setMessageId(req.getMessageId());
        request.setFromOperatorNo(req.getFromOperatorNo());
        request.setChatId(req.getChatId());
        request.setChatType(req.getChatType());
        request.setContent(req.getContent());
        request.setCategory(req.getCategory());
        request.setToOperatorNo(req.getToOperatorNo());
        kafkaTemplate.send(TOPIC_C2C_MESSAGE_SEND, request.getToOperatorNo(), JSONUtil.toJsonStr(request));
    }
}
