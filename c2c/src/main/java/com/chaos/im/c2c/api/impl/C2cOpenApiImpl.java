package com.chaos.im.c2c.api.impl;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.chaos.im.c2c.api.C2cOpenApi;
import com.chaos.im.c2c.api.domain.MessageSendReq;
import com.chaos.im.c2c.service.MessageSendService;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@DubboService(version = "1.0.0", interfaceClass = C2cOpenApi.class)
public class C2cOpenApiImpl implements C2cOpenApi {

    @Autowired
    private MessageSendService messageSendService;

    @Override
    public void sendMessage(MessageSendReq req) {
        messageSendService.sendMessage(req);
    }

    
}
