go:
  mvc:
    responseFilter: /actuator/prometheus

dubbo:
  application:
    name: c2c
    qos-port: 33333
  consumer:
    group: chaos
  protocol:
    port: 20886
  provider:
    group: chaos
  registry:
    address: zookeeper://${ZOOKEEPER_HOST:zookeeper}:${ZOOKEEPER_PORT:2181}
    check: false

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: false
    db-config:
      id-type: auto

server:
  port: 8094
  servlet:
    context-path: /c2c

spring:
  application:
    name: c2c
  data:
    mongodb:
      database: ${MONGODB_DATABASE:REPORT_BEHAVIOR}
      host: ${MONGODB_HOST:mongodb}
      password: ${MONGODB_PASSWORD:report_behavior_2020}
      port: ${MONGODB_PORT:27017}
      username: ${MONGODB_USERNAME:report_behavior}
  datasource:
    asyncInit: true
    driver-class-name: com.mysql.cj.jdbc.Driver
    filters: stat
    initialSize: 1
    maxActive: 20
    maxOpenPreparedStatements: 20
    maxWait: 60000
    minEvictableIdleTimeMillis: 300000
    minIdle: 1
    password: ${MYSQL_PASSWORD:wownowim_docker_2025}
    poolPreparedStatements: true
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 60000
    url: jdbc:mysql://${MYSQL_HOST:mysql}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:wownowim_docker}?useSSL=false&serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&allowPublicKeyRetrieval=true
    username: ${MYSQL_USERNAME:wownowim_docker}
  kafka:
    consumer:
      auto-offset-reset: latest
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka:9092}
      enable-auto-commit: false
      group-id: c2c
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      ack-mode: manual_immediate
      type: batch
    producer:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka:9092}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
  main:
    allow-bean-definition-overriding: true
    banner-mode: 'off'
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms

# 日志配置
logging:
  level:
    com.chaos.im.c2c: INFO
    org.springframework.kafka: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/c2c.log
    max-size: 100MB
    max-history: 30

# 监控端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
