#config:
#    db:
#        dialect: oracle
#        password: lifekh_mp_conf_2020
#        url: ********************************************
#        username: lifekh_mp_conf
go:
    mvc:
        responseFilter: /actuator/prometheus
dubbo:
    application:
        name: c2c
        qos-port: 33333
    consumer:
        group: chaos
    protocol:
        port: 20886
    provider:
        group: chaos
    registry:
        address: zookeeper://*************:2181
        check: false
mybatis-plus:
    configuration:
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
        banner: false
        db-config:
            id-type: auto
server:
    port: 8094
    servlet:
        context-path: /c2c
spring:
    application:
        name: c2c
    data:
        mongodb:
            database: REPORT_BEHAVIOR
            host: ************
            password: report_behavior_2020
            port: 27017
            username: report_behavior
    datasource:
        asyncInit: true
        driver-class-name: com.mysql.cj.jdbc.Driver
        filters: stat
        initialSize: 1
        maxActive: 3
        maxOpenPreparedStatements: 20
        maxWait: 60000
        minEvictableIdleTimeMillis: 300000
        minIdle: 1
        password: wownowim_sit_2025
        poolPreparedStatements: true
        testOnBorrow: false
        testOnReturn: false
        testWhileIdle: true
        timeBetweenEvictionRunsMillis: 60000
        url: ***************************************************************************************************************************************************************
        username: wownowim_sit
    kafka:
        consumer:
            auto-offset-reset: latest
            bootstrap-servers: ************:39092,************:39092,************:39092
            enable-auto-commit: false
            group-id: c2c
            key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
            value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        listener:
            ack-mode: manual_immediate
            type: batch
        producer:
            bootstrap-servers: ************:39092,************:39092,************:39092
            key-serializer: org.apache.kafka.common.serialization.StringSerializer
            value-serializer: org.apache.kafka.common.serialization.StringSerializer
    main:
        allow-bean-definition-overriding: true
        banner-mode: 'off'
    redis:
        host: *************
        port: 6379
