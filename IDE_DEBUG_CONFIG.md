# IDE调试配置指南

本文档介绍如何在IDE中配置远程调试，以便在Docker容器中调试应用。

## IntelliJ IDEA配置

### 1. 创建远程调试配置

对于每个模块，创建一个远程调试配置：

1. 打开 `Run/Debug Configurations`
2. 点击 `+` 添加新配置
3. 选择 `Remote JVM Debug`
4. 配置如下参数：

#### C2C模块
- **Name**: `C2C Remote Debug`
- **Host**: `localhost`
- **Port**: `5005`
- **Command line arguments**: `-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005`

#### C2G模块
- **Name**: `C2G Remote Debug`
- **Host**: `localhost`
- **Port**: `5006`
- **Command line arguments**: `-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5006`

#### Chat模块
- **Name**: `Chat Remote Debug`
- **Host**: `localhost`
- **Port**: `5007`
- **Command line arguments**: `-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5007`

#### Gateway-TCP模块
- **Name**: `Gateway-TCP Remote Debug`
- **Host**: `localhost`
- **Port**: `5008`
- **Command line arguments**: `-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5008`

#### Route模块
- **Name**: `Route Remote Debug`
- **Host**: `localhost`
- **Port**: `5009`
- **Command line arguments**: `-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5009`

#### ID-Server模块
- **Name**: `ID-Server Remote Debug`
- **Host**: `localhost`
- **Port**: `5010`
- **Command line arguments**: `-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5010`

### 2. 使用调试配置

1. 启动开发环境: `./dev-hot-reload.sh start`
2. 等待容器启动完成
3. 在IDEA中选择对应的远程调试配置
4. 点击Debug按钮连接到容器
5. 在代码中设置断点进行调试

## VS Code配置

### 1. 创建launch.json

在项目根目录的`.vscode/launch.json`中添加以下配置：

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Debug C2C Remote",
            "request": "attach",
            "hostName": "localhost",
            "port": 5005
        },
        {
            "type": "java",
            "name": "Debug C2G Remote",
            "request": "attach",
            "hostName": "localhost",
            "port": 5006
        },
        {
            "type": "java",
            "name": "Debug Chat Remote",
            "request": "attach",
            "hostName": "localhost",
            "port": 5007
        },
        {
            "type": "java",
            "name": "Debug Gateway-TCP Remote",
            "request": "attach",
            "hostName": "localhost",
            "port": 5008
        },
        {
            "type": "java",
            "name": "Debug Route Remote",
            "request": "attach",
            "hostName": "localhost",
            "port": 5009
        },
        {
            "type": "java",
            "name": "Debug ID-Server Remote",
            "request": "attach",
            "hostName": "localhost",
            "port": 5010
        }
    ]
}
```

### 2. 使用调试配置

1. 启动开发环境: `./dev-hot-reload.sh start`
2. 在VS Code中按 `F5` 或点击调试按钮
3. 选择对应的远程调试配置
4. 设置断点进行调试

## Eclipse配置

### 1. 创建远程调试配置

1. 右键项目 → `Debug As` → `Debug Configurations...`
2. 双击 `Remote Java Application` 创建新配置
3. 配置连接属性：
   - **Project**: 选择对应的模块项目
   - **Connection Type**: `Standard (Socket Attach)`
   - **Host**: `localhost`
   - **Port**: 对应模块的调试端口

### 2. 调试步骤

1. 启动开发环境
2. 在Eclipse中启动远程调试配置
3. 设置断点进行调试

## 调试技巧

### 1. 热重载调试

当修改代码后：
1. 保存文件
2. 运行 `./dev-hot-reload.sh reload <module>` 重载模块
3. 调试器会自动重新连接
4. 新的断点和代码修改会生效

### 2. 日志调试

```bash
# 查看特定模块的实时日志
./dev-hot-reload.sh logs c2c

# 查看所有应用日志
./dev-hot-reload.sh logs
```

### 3. 文件监控自动重载

```bash
# 监控C2C模块文件变化并自动重载
./dev-hot-reload.sh watch c2c

# 监控所有模块（需要多个终端窗口）
./dev-hot-reload.sh watch
```

### 4. 数据库调试

连接到开发环境的数据库：

**MySQL**:
- Host: `localhost`
- Port: `3306`
- Database: `wownowim_docker`
- Username: `wownowim_docker`
- Password: `wownowim_docker_2025`

**MongoDB**:
- Host: `localhost`
- Port: `27017`
- Database: `REPORT_BEHAVIOR`
- Username: `admin`
- Password: `admin123456`

**Redis**:
- Host: `localhost`
- Port: `6379`

## 常见问题

### 1. 调试器连接失败

**问题**: 无法连接到远程调试端口

**解决方案**:
1. 确认容器已启动: `docker-compose -f docker-compose.dev.yml ps`
2. 检查端口是否开放: `netstat -an | grep 5005`
3. 查看容器日志: `./dev-hot-reload.sh logs c2c`

### 2. 断点不生效

**问题**: 设置的断点没有被触发

**解决方案**:
1. 确认代码已重新编译: `./dev-hot-reload.sh compile c2c`
2. 重载模块: `./dev-hot-reload.sh reload c2c`
3. 检查断点设置的类是否正确加载

### 3. 热重载不工作

**问题**: 修改代码后没有自动重载

**解决方案**:
1. 确认Spring Boot DevTools已添加到依赖中
2. 检查IDE是否自动编译了class文件
3. 手动重载模块: `./dev-hot-reload.sh reload <module>`

### 4. 性能问题

**问题**: 开发环境运行缓慢

**解决方案**:
1. 增加Docker内存限制
2. 调整JVM参数减少内存使用
3. 只启动需要调试的模块

## 最佳实践

1. **模块化调试**: 只启动和调试当前开发的模块
2. **日志级别**: 在开发环境中使用DEBUG日志级别
3. **数据隔离**: 使用独立的开发数据库避免影响其他环境
4. **定期清理**: 定期清理开发环境数据和镜像
5. **版本控制**: 不要提交IDE特定的调试配置文件
