# Docker热启动开发指南

本指南介绍如何使用Docker热启动功能进行高效的Java微服务开发。

## 🚀 快速开始

### 1. 启动开发环境

```bash
# 一键启动完整开发环境
./dev-hot-reload.sh start
```

这个命令会：
- 编译所有模块
- 启动所有基础服务（MySQL、Redis、Kafka等）
- 启动所有应用服务（C2C、C2G、Chat等）
- 开启远程调试端口

### 2. 开发工作流

```bash
# 1. 修改代码（在IDE中编辑Java文件）
# 2. 热重载模块
./dev-hot-reload.sh reload c2c

# 3. 查看日志确认重载成功
./dev-hot-reload.sh logs c2c

# 4. 在IDE中连接远程调试器进行调试
```

## 🔧 核心功能

### 热重载机制

我们的热启动方案包含三个层次：

1. **文件级热重载**: 通过Docker Volume挂载编译后的class文件
2. **应用级热重载**: 使用Spring Boot DevTools自动重启
3. **容器级热重载**: 快速重启容器加载新代码

### 远程调试

每个模块都配置了独立的远程调试端口：

| 模块 | 调试端口 | 应用端口 |
|------|----------|----------|
| C2C | 5005 | 8094 |
| C2G | 5006 | 8096 |
| Chat | 5007 | 8095 |
| Gateway-TCP | 5008 | 9094 |
| Route | 5009 | 9097 |
| ID-Server | 5010 | 8100 |

## 📋 详细命令说明

### 环境管理

```bash
# 启动开发环境
./dev-hot-reload.sh start

# 停止开发环境
./dev-hot-reload.sh stop

# 重启开发环境
./dev-hot-reload.sh restart

# 显示环境信息
./dev-hot-reload.sh info

# 清理开发环境（删除所有数据）
./dev-hot-reload.sh clean
```

### 编译和重载

```bash
# 编译所有模块
./dev-hot-reload.sh compile

# 编译单个模块
./dev-hot-reload.sh compile c2c

# 热重载单个模块
./dev-hot-reload.sh reload c2c

# 监控文件变化并自动重载（需要安装fswatch或inotify-tools）
./dev-hot-reload.sh watch c2c
```

### 日志查看

```bash
# 查看所有应用日志
./dev-hot-reload.sh logs

# 查看单个模块日志
./dev-hot-reload.sh logs c2c

# 查看基础服务日志
docker-compose -f docker-compose.dev.yml logs mysql
docker-compose -f docker-compose.dev.yml logs redis
```

## 🛠️ 开发最佳实践

### 1. 高效的开发流程

```bash
# 第一次启动
./dev-hot-reload.sh start

# 开发过程中
# 1. 在IDE中修改代码
# 2. IDE自动编译（确保开启自动编译）
# 3. 执行热重载
./dev-hot-reload.sh reload c2c

# 4. 测试功能
curl http://localhost:8094/c2c/actuator/health

# 5. 查看日志
./dev-hot-reload.sh logs c2c
```

### 2. 调试工作流

```bash
# 1. 启动开发环境
./dev-hot-reload.sh start

# 2. 在IDE中配置远程调试（参考IDE_DEBUG_CONFIG.md）

# 3. 连接调试器到对应端口
# C2C: localhost:5005
# C2G: localhost:5006
# 等等...

# 4. 设置断点并开始调试

# 5. 修改代码后重载
./dev-hot-reload.sh reload c2c

# 6. 调试器会自动重新连接
```

### 3. 多模块开发

```bash
# 同时开发多个模块时，可以分别重载
./dev-hot-reload.sh reload c2c
./dev-hot-reload.sh reload c2g

# 或者在不同终端窗口监控不同模块
# 终端1
./dev-hot-reload.sh watch c2c

# 终端2  
./dev-hot-reload.sh watch c2g
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 热重载不生效

**症状**: 修改代码后应用行为没有变化

**解决步骤**:
```bash
# 1. 检查编译是否成功
./dev-hot-reload.sh compile c2c

# 2. 手动重载模块
./dev-hot-reload.sh reload c2c

# 3. 检查容器状态
docker-compose -f docker-compose.dev.yml ps

# 4. 查看容器日志
./dev-hot-reload.sh logs c2c
```

#### 2. 远程调试连接失败

**症状**: IDE无法连接到调试端口

**解决步骤**:
```bash
# 1. 检查容器是否运行
docker-compose -f docker-compose.dev.yml ps

# 2. 检查端口是否开放
netstat -an | grep 5005

# 3. 重启容器
docker-compose -f docker-compose.dev.yml restart c2c-app-dev

# 4. 查看容器日志
./dev-hot-reload.sh logs c2c
```

#### 3. 编译错误

**症状**: 编译失败或找不到依赖

**解决步骤**:
```bash
# 1. 清理并重新编译
mvn clean compile -pl c2c -am

# 2. 检查依赖
mvn dependency:tree -pl c2c

# 3. 重新复制依赖
mvn dependency:copy-dependencies -DoutputDirectory=target/lib -pl c2c
```

#### 4. 容器启动失败

**症状**: 容器无法启动或立即退出

**解决步骤**:
```bash
# 1. 查看容器日志
docker-compose -f docker-compose.dev.yml logs c2c-app-dev

# 2. 检查依赖服务状态
docker-compose -f docker-compose.dev.yml ps

# 3. 重新构建镜像
docker-compose -f docker-compose.dev.yml build c2c-app-dev

# 4. 重启服务
docker-compose -f docker-compose.dev.yml up -d c2c-app-dev
```

## 📊 性能优化

### 1. 减少重载时间

```bash
# 只重载修改的模块，而不是所有模块
./dev-hot-reload.sh reload c2c

# 使用文件监控自动重载
./dev-hot-reload.sh watch c2c
```

### 2. 内存优化

在开发环境中，可以调整JVM参数减少内存使用：

```bash
# 编辑docker-compose.dev.yml中的JAVA_OPTS
JAVA_OPTS="-Xms128m -Xmx256m ..."
```

### 3. 只启动需要的服务

```bash
# 只启动基础服务和特定应用
docker-compose -f docker-compose.dev.yml up -d mysql redis zookeeper c2c-app-dev
```

## 🔧 高级配置

### 1. 自定义JVM参数

编辑各模块的`Dockerfile.dev`文件中的`JAVA_OPTS`环境变量：

```dockerfile
ENV JAVA_OPTS="-Xms256m -Xmx512m \
    -XX:+UseG1GC \
    -Dspring.devtools.restart.enabled=true \
    -Dspring.profiles.active=dev \
    -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
```

### 2. 自定义监控配置

可以添加更多的监控和调试工具：

```dockerfile
# 添加JVM监控
ENV JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote \
    -Dcom.sun.management.jmxremote.port=9999 \
    -Dcom.sun.management.jmxremote.authenticate=false \
    -Dcom.sun.management.jmxremote.ssl=false"
```

### 3. 文件监控工具安装

**macOS**:
```bash
brew install fswatch
```

**Linux (Ubuntu/Debian)**:
```bash
sudo apt-get install inotify-tools
```

**Linux (CentOS/RHEL)**:
```bash
sudo yum install inotify-tools
```

## 📚 相关文档

- [IDE调试配置指南](IDE_DEBUG_CONFIG.md)
- [Docker部署指南](DOCKER_DEPLOYMENT.md)
- [服务启动顺序说明](STARTUP_ORDER.md)

## 🤝 贡献指南

如果你发现问题或有改进建议：

1. 查看现有的issue
2. 创建新的issue描述问题
3. 提交Pull Request

## 📞 获取帮助

如果遇到问题：

1. 查看本指南的故障排除部分
2. 检查相关日志文件
3. 联系开发团队获取支持
