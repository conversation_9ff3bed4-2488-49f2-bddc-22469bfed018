package com.chaos.keep.alive.client.mock;

import com.alibaba.fastjson.JSON;
import com.chaos.keep.alive.client.listener.CommandListener;
import com.chaos.keep.alive.client.mock.config.MockPropertiesUtils;
import com.chaos.keep.alive.client.IClient;
import com.chaos.keep.alive.client.tcp.TcpClient;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.im.constant.MessageType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.protobuf.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 */
@Slf4j
public class MockKaClient {

    private static final String MOCK_TOKEN = "1278941701185900544";
    private static final String MOCK_TOKEN2 = "1299356905457774592";
    private static final String MOCK_TOKEN3 = "1278316867292663808";

    private static final String MOCK_OPERATOR_NO = "1278941701185900544";
    private static final String MOCK_OPERATOR_NO2 = "1299356905457774592";
    private static final String MOCK_OPERATOR_NO3 = "1278316867292663808";

    private static final String MOCK_DEVICE_ID = "E1FBA8B0B784ACBE15FB0F18F81CBE131E76608B";
    private static final String MOCK_DEVICE_ID2 = "5E00EC8D284F0F1A56319A8F9C5A41FBD7572269";
    private static final String MOCK_DEVICE_ID3 = "5E00EC8D284F0F1A56319A8F9C5A41FBD7572269";

    private static final String MOCK_APP_ID = "SuperApp";

    private static final Integer MOCK_APP_NO = 11;

    private static final Long MOCK_DRIVER_ID = 19L;

    public void start() {
        for (long i = 0; i < MockPropertiesUtils.getConnectThreadCount(); i++) {
            new Thread(new MockRunner(i)).start();
        }
    }

    static class MockRunner implements Runnable {
        private final Long threadId;

        MockRunner(Long threadId) {
            this.threadId = threadId;
        }

        @Override
        public void run() {
            for (int i = 0; i < MockPropertiesUtils.getConnectLoopCount(); i++) {

                IClient client = new TcpClient();
                MockCommandListener commandListener = new MockCommandListener(client);
//
                IClient client2 = new TcpClient();
                MockCommandListener commandListener2 = new MockCommandListener(client2);
//
//
//                IClient client3 = new TcpClient(); //new WebsocketClient();
//                MockCommandListener commandListener3 = new MockCommandListener(client3);

                client.connect(MOCK_TOKEN, MOCK_OPERATOR_NO, MOCK_DEVICE_ID, MOCK_APP_ID, MOCK_APP_NO, commandListener);
//                client.fetchC2cOfflineMessage(7L, 10L, 1L, MOCK_OPERATOR_NO, MOCK_DEVICE_ID);
//
                client2.connect(MOCK_TOKEN2, MOCK_OPERATOR_NO2, MOCK_DEVICE_ID2, "Delivery", MOCK_APP_NO, commandListener2);
//                client2.fetchC2gOfflineMessage(46L, 10L, 1L, MOCK_OPERATOR_NO2, MOCK_DEVICE_ID2);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
//////
//                client3.connect(MOCK_TOKEN3, MOCK_OPERATOR_NO3, MOCK_DEVICE_ID3, MOCK_APP_ID, MOCK_APP_NO, commandListener3);
//                client3.fetchC2gOfflineMessage(45L, 10L, 1L, MOCK_OPERATOR_NO3, MOCK_DEVICE_ID3);
//
                for (int j = 0; j < MockPropertiesUtils.getReportLocationLoopCount(); j++) {
                    //                for(int j=0;j<1;j++) {
                    try {
                        Thread.sleep(1000);
                        if (client.isConnected()) {
                            //                            client2.reportRiderLocation(10996L, "Delivery",System.currentTimeMillis(),23.24,104.25);
                            //                            client2.sendText(68L,"1853772398078423040","...............",1);
                            client.sendC2cMessage(105L, "1299356905457774592", "{\"text\":\"单聊消息管你什么hello world" + j + "\"}", 1);
//                            client.sendC2gMessage(112L,  "{\"text\":\"群聊管你什么hello world" + j + "\"}", 1);
                        } else {
                            log.info("token验证失败");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }


//                for(int j=0;j<MockPropertiesUtils.getReportLocationLoopCount();j++) {
//                    try {
//                        Thread.sleep(3000);
//                        if (client3.isConnected()) {
//                            client3.sendC2gMessage(21L, "管你什么hello world",1);
//                        } else {
//                            log.info("token验证失败");
//                        }
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }


//                for (int j = 0; j < MockPropertiesUtils.getReportLocationLoopCount(); j++) {
//                    try {
//                        Thread.sleep(10000);
//                        if (client2.isConnected()) {
//                            client2.sendC2cMessage(26L,"1891485424355852288", "WS 单聊消息hello world 第" + j + "次", 1);
//                        } else {
//                            log.info("token验证失败");
//                        }
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }
                try {
                    Thread.sleep(300000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        static class MockCommandListener implements CommandListener {

            private static final long MESSAGE_COUNT_END = 1L;

            private final IClient client;

            public MockCommandListener(IClient client) {
                this.client = client;
            }

            @Override
            @SneakyThrows
            public void onCommand(Command command) {
                if (command.getBizType() == CommandType.COMMAND_ONLINE) {
                    Result result = Result.parseFrom(command.getBody());
                    if (result.getSuccess()) {
                        log.info("上线成功");
                    } else if (command.getBizType() == CommandType.COMMAND_GONOW_DRIVER_REPORT_LOCATION) {
                        log.info("上报成功");
                    } else {
                        log.info("认证失败");
                        // 处理认证失败，退出到登录画面，客户端重新登录
                    }
                } else if (command.getBizType() == CommandType.COMMAND_GONOW_ORDER_STATUS_CHANGE) {
                    MessagePush messagePush = MessagePush.parseFrom(command.getBody());

                    log.info("接收到订单状态变更通知, operatorNo:{},deviceId:{},content:{}", command.getOperatorNo(), command.getDeviceId(), messagePush.getContent());
                } else if (command.getBizType() == CommandType.COMMAND_GONOW_ORDER_BASIC_INFO) {
                    MessagePush messagePush = MessagePush.parseFrom(command.getBody());

                    log.info("接收到订单基础信息, operatorNo:{},deviceId:{},content:{}", command.getOperatorNo(), command.getDeviceId(), messagePush.getContent());
                } else if (command.getBizType() == CommandType.COMMAND_OFFLINE) {
                    Result result = Result.parseFrom(command.getBody());
                    log.info("服务端断开连接,原因:{}", result.getErrorMessage());
                } else if (command.getBizType() == CommandType.COMMAND_GONOW_DRIVER_REPORT_LOCATION) {
                    Result result = Result.parseFrom(command.getBody());
                    if (result.getSuccess()) {
                        log.info("上报位置成功");
                    } else {
                        log.info("上报位置失败," + result.getErrorMessage());
                    }
                } else if (command.getBizType() == com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2G_MESSAGE_SEND) {
                    if (command.getMessageType() == MessageType.MESSAGE_TYPE_PUSH) {
                        ImMessagePush imMessagePush = ImMessagePush.parseFrom(command.getBody());
                        log.info("toOperatorNo:{} 接收到群聊消息,messageId:{},content:{} ,category:{},sequence:{}", imMessagePush.getToOperatorNo(), imMessagePush.getMessageId(),
                                imMessagePush.getContent(), imMessagePush.getCategory(), imMessagePush.getSequence());
                        client.sendC2gAck(imMessagePush.getChatId(), imMessagePush.getMessageId(), imMessagePush.getChatType());
                    } else if (command.getMessageType() == MessageType.MESSAGE_TYPE_SERVER_ACK) {
                        log.info("客户端接收服务端发送的群聊消息ack messageType:{}", command.getMessageType());
                    }

                } else if (command.getBizType() == com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2C_MESSAGE_SEND) {
                    if (command.getMessageType() == MessageType.MESSAGE_TYPE_SERVER_ACK) {
                        log.info("客户端接收服务端发送的单聊消息ack messageType:{}", command.getMessageType());
                    } else {
                        ImMessagePush imMessagePush = ImMessagePush.parseFrom(command.getBody());
                        log.info("toOperatorNo:{} 接收到单聊消息,messageId:{},content:{} ,category:{}, sequence:{}", imMessagePush.getToOperatorNo(), imMessagePush.getMessageId(),
                                imMessagePush.getContent(), imMessagePush.getCategory(), imMessagePush.getSequence());
                    client.sendC2cAck(imMessagePush.getChatId(),imMessagePush.getToOperatorNo(), imMessagePush.getMessageId(), imMessagePush.getChatType());
                    }
                } else if (command.getBizType() == CommandType.COMMAND_WOWNOW_RIDER_REPORT_LOCATION) {
                    Result result = Result.parseFrom(command.getBody());
                    if (result.getSuccess()) {
                        log.info("骑手上报位置成功");
                    } else {
                        log.info("骑手上报位置失败," + result.getErrorMessage());
                    }
                }
            }

            @Override
            public void onCommand(JsonCommand command) {
                if (command.getBizType() == com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2C_MESSAGE_SEND) {
                    MessageJsonPush messageJsonPush = JSON.parseObject(command.getBody(), MessageJsonPush.class);

                    log.info("toOperatorNo:{}接收到ws单聊消息,messageId:{},content:{} ,category:{}", messageJsonPush.getToOperatorNo(), messageJsonPush.getMessageId(),
                            messageJsonPush.getContent(), messageJsonPush.getCategory());
                    client.sendC2cAck(messageJsonPush.getChatId(), messageJsonPush.getToOperatorNo(), messageJsonPush.getMessageId(), messageJsonPush.getChatType());

                } else if (command.getBizType() == com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2G_MESSAGE_SEND) {
                    MessageJsonPush messageJsonPush = JSON.parseObject(command.getBody(), MessageJsonPush.class);

                    log.info("toOperatorNo:{}接收到ws群聊消息,messageId:{},content:{} ,category:{}", messageJsonPush.getToOperatorNo(), messageJsonPush.getMessageId(),
                            messageJsonPush.getContent(), messageJsonPush.getCategory());
                    client.sendC2gAck(messageJsonPush.getChatId(), messageJsonPush.getMessageId(), messageJsonPush.getChatType());
                }
            }


        }
    }
}
