version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: c2c-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: wownowim_docker
      MYSQL_USER: wownowim_docker
      MYSQL_PASSWORD: wownowim_docker_2025
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - c2c-network
    restart: unless-stopped

  # MongoDB
  mongodb:
    image: mongo:4.4
    container_name: c2c-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123456
      MONGO_INITDB_DATABASE: REPORT_BEHAVIOR
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - c2c-network
    restart: unless-stopped

  # Redis
  redis:
    image: redis:6.2-alpine
    container_name: c2c-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - c2c-network
    restart: unless-stopped

  # Zookeeper
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    container_name: c2c-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - c2c-network
    restart: unless-stopped

  # Kafka
  kafka:
    image: confluentinc/cp-kafka:7.0.1
    container_name: c2c-kafka
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    ports:
      - "29092:29092"
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - c2c-network
    restart: unless-stopped

  # C2C应用
  c2c-app:
    build:
      context: ./c2c
      dockerfile: Dockerfile
    container_name: c2c-application
    depends_on:
      - mysql
      - mongodb
      - redis
      - kafka
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: wownowim_docker
      MYSQL_USERNAME: wownowim_docker
      MYSQL_PASSWORD: wownowim_docker_2025
      MONGODB_HOST: mongodb
      MONGODB_PORT: 27017
      MONGODB_DATABASE: REPORT_BEHAVIOR
      MONGODB_USERNAME: report_behavior
      MONGODB_PASSWORD: report_behavior_2020
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
    ports:
      - "8094:8094"
      - "20886:20886"
    volumes:
      - c2c_logs:/app/logs
    networks:
      - c2c-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8094/c2c/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # C2G应用
  c2g-app:
    build:
      context: ./c2g
      dockerfile: Dockerfile
    container_name: c2g-application
    depends_on:
      - mysql
      - mongodb
      - redis
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: wownowim_docker
      MYSQL_USERNAME: wownowim_docker
      MYSQL_PASSWORD: wownowim_docker_2025
      MONGODB_HOST: mongodb
      MONGODB_PORT: 27017
      MONGODB_DATABASE: REPORT_BEHAVIOR
      MONGODB_USERNAME: report_behavior
      MONGODB_PASSWORD: report_behavior_2020
      REDIS_HOST: redis
      REDIS_PORT: 6379
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
    ports:
      - "8096:8096"
      - "20883:20883"
    volumes:
      - c2g_logs:/app/logs
    networks:
      - c2c-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8096/c2g/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Chat应用
  chat-app:
    build:
      context: ./chat
      dockerfile: Dockerfile
    container_name: chat-application
    depends_on:
      - kafka
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
    ports:
      - "8095:8095"
      - "20882:20882"
    volumes:
      - chat_logs:/app/logs
    networks:
      - c2c-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8095/chat/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Gateway-TCP应用
  gateway-tcp-app:
    build:
      context: ./gateway-tcp
      dockerfile: Dockerfile
    container_name: gateway-tcp-application
    depends_on:
      - redis
      - zookeeper
      - route-app
    environment:
      SPRING_PROFILES_ACTIVE: docker
      REDIS_HOST: redis
      REDIS_PORT: 6379
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
      GATEWAY_TCP_PORT: 38888
      GATEWAY_SERVER_ID: gateway-tcp-1
      ROUTE_SERVERS: route:9670
    ports:
      - "9094:9094"
      - "38888:38888"
    volumes:
      - gateway_tcp_logs:/app/logs
    networks:
      - c2c-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9094/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Route应用
  route-app:
    build:
      context: ./route
      dockerfile: Dockerfile
    container_name: route-application
    depends_on:
      - redis
      - kafka
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
      ROUTE_TCP_PORT: 9670
      ROUTE_SERVER_ID: keep-alive-route
    ports:
      - "9097:9097"
      - "9670:9670"
      - "20990:20990"
    volumes:
      - route_logs:/app/logs
    networks:
      - c2c-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9097/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ID-Server应用
  id-server-app:
    build:
      context: ./id-server
      dockerfile: Dockerfile
    container_name: id-server-application
    depends_on:
      - mysql
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: wownowim_docker
      MYSQL_USERNAME: wownowim_docker
      MYSQL_PASSWORD: wownowim_docker_2025
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
    ports:
      - "8100:8100"
      - "20887:20887"
    volumes:
      - id_server_logs:/app/logs
    networks:
      - c2c-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8100/id-server/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  mysql_data:
  mongodb_data:
  redis_data:
  zookeeper_data:
  zookeeper_logs:
  kafka_data:
  c2c_logs:

networks:
  c2c-network:
    driver: bridge
