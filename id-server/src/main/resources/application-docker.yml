go:
  mvc:
    responseFilter: /actuator/prometheus

camellia-id-gen-segment:
  max-retry: 100
  region-bits: 0
  region-id: 0
  region-id-shifting-bits: 0
  retry-interval-millis: 10
  step: 1000
  tag-count: 1000

server:
  port: 8100
  servlet:
    context-path: /id-server

spring:
  application:
    name: id-server
  datasource:
    asyncInit: true
    driver-class-name: com.mysql.cj.jdbc.Driver
    filters: stat
    initialSize: 50
    maxActive: 200
    maxOpenPreparedStatements: 20
    maxWait: 60000
    minEvictableIdleTimeMillis: 300000
    minIdle: 1
    password: ${MYSQL_PASSWORD:wownowim_docker_2025}
    poolPreparedStatements: true
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 60000
    url: jdbc:mysql://${MYSQL_HOST:mysql}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:wownowim_docker}?useSSL=false&serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&allowPublicKeyRetrieval=true
    username: ${MYSQL_USERNAME:wownowim_docker}
  main:
    allow-bean-definition-overriding: true
    banner-mode: 'off'

dubbo:
  application:
    name: id-server
    qos-port: 33333
  consumer:
    group: chaos
  protocol:
    port: 20887
  provider:
    group: chaos
  registry:
    address: zookeeper://${ZOOKEEPER_HOST:zookeeper}:${ZOOKEEPER_PORT:2181}
    check: false

# 日志配置
logging:
  level:
    com.chaos.keep.alive.im.id.server: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/id-server.log

# 监控端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
