<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>camellia-id-gen-spring-boot-starters</artifactId>
    <version>1.0.51</version>

    <packaging>pom</packaging>

    <parent>
        <groupId>com.netease.nim</groupId>
        <artifactId>camellia-spring-boot-starters</artifactId>
        <version>1.0.51</version>
    </parent>

    <modules>
        <module>camellia-id-gen-id-loader</module>
        <module>camellia-id-gen-snowflake-spring-boot-starter</module>
        <module>camellia-id-gen-strict-spring-boot-starter</module>
        <module>camellia-id-gen-segment-spring-boot-starter</module>
    </modules>

</project>
