#!/bin/bash

# Docker网络问题修复脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查网络连接
check_network() {
    log_step "检查网络连接..."
    
    # 检查基本网络连接
    if ping -c 1 8.8.8.8 > /dev/null 2>&1; then
        log_info "基本网络连接正常"
    else
        log_error "网络连接异常，请检查网络设置"
        return 1
    fi
    
    # 检查DNS解析
    if nslookup docker.io > /dev/null 2>&1; then
        log_info "DNS解析正常"
    else
        log_warn "DNS解析可能有问题"
    fi
    
    # 检查Docker Hub连接
    if curl -s --connect-timeout 10 https://registry-1.docker.io/v2/ > /dev/null; then
        log_info "Docker Hub连接正常"
    else
        log_warn "Docker Hub连接超时，建议使用镜像加速器"
    fi
}

# 清理Docker缓存
clean_docker_cache() {
    log_step "清理Docker缓存..."
    
    # 停止所有容器
    log_info "停止所有运行中的容器..."
    docker stop $(docker ps -q) 2>/dev/null || true
    
    # 清理构建缓存
    log_info "清理Docker构建缓存..."
    docker builder prune -f
    
    # 清理镜像缓存
    log_info "清理未使用的镜像..."
    docker image prune -f
    
    # 清理网络
    log_info "清理未使用的网络..."
    docker network prune -f
    
    # 清理卷
    log_info "清理未使用的卷..."
    docker volume prune -f
    
    log_info "Docker缓存清理完成"
}

# 重启Docker服务
restart_docker() {
    log_step "重启Docker服务..."
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        log_info "macOS系统，请手动重启Docker Desktop"
        log_info "1. 点击Docker Desktop图标"
        log_info "2. 选择 'Restart'"
        log_info "3. 等待Docker重启完成"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_info "重启Docker服务..."
        sudo systemctl restart docker
        sleep 5
        if systemctl is-active --quiet docker; then
            log_info "Docker服务重启成功"
        else
            log_error "Docker服务重启失败"
            return 1
        fi
    else
        log_warn "未知操作系统，请手动重启Docker"
    fi
}

# 测试镜像拉取
test_image_pull() {
    log_step "测试镜像拉取..."
    
    local test_images=(
        "alpine:latest"
        "registry.cn-hangzhou.aliyuncs.com/library/alpine:latest"
    )
    
    for image in "${test_images[@]}"; do
        log_info "测试拉取镜像: $image"
        if timeout 60 docker pull "$image"; then
            log_info "✅ 成功拉取: $image"
            docker rmi "$image" 2>/dev/null || true
            return 0
        else
            log_warn "❌ 拉取失败: $image"
        fi
    done
    
    log_error "所有测试镜像拉取失败"
    return 1
}

# 配置Docker超时设置
configure_timeout() {
    log_step "配置Docker超时设置..."
    
    # 设置环境变量
    export DOCKER_CLIENT_TIMEOUT=120
    export COMPOSE_HTTP_TIMEOUT=120
    
    log_info "已设置Docker客户端超时为120秒"
    log_info "请在当前会话中使用，或将以下内容添加到 ~/.bashrc 或 ~/.zshrc:"
    echo "export DOCKER_CLIENT_TIMEOUT=120"
    echo "export COMPOSE_HTTP_TIMEOUT=120"
}

# 使用代理拉取镜像
pull_with_proxy() {
    local image=$1
    log_step "尝试使用代理拉取镜像: $image"
    
    # 常用的镜像映射
    declare -A image_map=(
        ["openjdk:8-jdk-alpine"]="registry.cn-hangzhou.aliyuncs.com/library/openjdk:8-jdk-alpine"
        ["openjdk:8-jre-alpine"]="registry.cn-hangzhou.aliyuncs.com/library/openjdk:8-jre-alpine"
        ["mysql:8.0"]="registry.cn-hangzhou.aliyuncs.com/library/mysql:8.0"
        ["redis:6.2-alpine"]="registry.cn-hangzhou.aliyuncs.com/library/redis:6.2-alpine"
        ["mongo:4.4"]="registry.cn-hangzhou.aliyuncs.com/library/mongo:4.4"
    )
    
    if [[ -n "${image_map[$image]}" ]]; then
        local mirror_image="${image_map[$image]}"
        log_info "使用镜像源: $mirror_image"
        
        if docker pull "$mirror_image"; then
            # 重新标记镜像
            docker tag "$mirror_image" "$image"
            log_info "✅ 成功拉取并标记镜像: $image"
            return 0
        fi
    fi
    
    log_error "❌ 无法拉取镜像: $image"
    return 1
}

# 预拉取所需镜像
pre_pull_images() {
    log_step "预拉取项目所需镜像..."
    
    local required_images=(
        "registry.cn-hangzhou.aliyuncs.com/library/openjdk:8-jdk-alpine"
        "registry.cn-hangzhou.aliyuncs.com/library/mysql:8.0"
        "registry.cn-hangzhou.aliyuncs.com/library/redis:6.2-alpine"
        "registry.cn-hangzhou.aliyuncs.com/library/mongo:4.4"
        "confluentinc/cp-zookeeper:7.0.1"
        "confluentinc/cp-kafka:7.0.1"
    )
    
    for image in "${required_images[@]}"; do
        log_info "拉取镜像: $image"
        if timeout 300 docker pull "$image"; then
            log_info "✅ 成功: $image"
        else
            log_warn "❌ 失败: $image"
        fi
    done
}

# 显示解决方案
show_solutions() {
    log_info "Docker网络问题解决方案："
    echo ""
    echo "🔧 方案1: 配置镜像加速器"
    echo "  ./docker-registry-config.sh configure"
    echo ""
    echo "🔧 方案2: 使用国内镜像源"
    echo "  已自动修改Dockerfile使用阿里云镜像源"
    echo ""
    echo "🔧 方案3: 清理Docker缓存"
    echo "  ./fix-docker-network.sh clean"
    echo ""
    echo "🔧 方案4: 重启Docker服务"
    echo "  ./fix-docker-network.sh restart"
    echo ""
    echo "🔧 方案5: 预拉取镜像"
    echo "  ./fix-docker-network.sh pre-pull"
    echo ""
    echo "🔧 方案6: 配置超时设置"
    echo "  ./fix-docker-network.sh timeout"
}

# 自动修复
auto_fix() {
    log_step "开始自动修复Docker网络问题..."
    
    # 1. 检查网络
    check_network
    
    # 2. 配置超时
    configure_timeout
    
    # 3. 清理缓存
    clean_docker_cache
    
    # 4. 预拉取镜像
    pre_pull_images
    
    # 5. 测试拉取
    if test_image_pull; then
        log_info "🎉 Docker网络问题修复成功！"
    else
        log_warn "⚠️ 自动修复未完全成功，请尝试手动配置镜像加速器"
        show_solutions
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_network
            ;;
        "clean")
            clean_docker_cache
            ;;
        "restart")
            restart_docker
            ;;
        "test")
            test_image_pull
            ;;
        "timeout")
            configure_timeout
            ;;
        "pre-pull")
            pre_pull_images
            ;;
        "pull")
            if [ -z "$2" ]; then
                log_error "请指定要拉取的镜像名称"
                exit 1
            fi
            pull_with_proxy "$2"
            ;;
        "auto")
            auto_fix
            ;;
        "solutions")
            show_solutions
            ;;
        "help"|*)
            echo "Docker网络问题修复脚本"
            echo ""
            echo "使用方法:"
            echo "  $0 check      - 检查网络连接"
            echo "  $0 clean      - 清理Docker缓存"
            echo "  $0 restart    - 重启Docker服务"
            echo "  $0 test       - 测试镜像拉取"
            echo "  $0 timeout    - 配置超时设置"
            echo "  $0 pre-pull   - 预拉取所需镜像"
            echo "  $0 pull <img> - 使用代理拉取指定镜像"
            echo "  $0 auto       - 自动修复"
            echo "  $0 solutions  - 显示解决方案"
            echo ""
            echo "示例:"
            echo "  $0 auto       - 自动修复网络问题"
            echo "  $0 pull openjdk:8-jdk-alpine"
            ;;
    esac
}

# 执行主函数
main "$@"
