[mysqld]
# 字符集配置
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# 认证插件配置（兼容旧版本客户端）
default_authentication_plugin=mysql_native_password

# 网络配置
bind-address=0.0.0.0
port=3306

# 性能优化
max_connections=200
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT

# 日志配置
general_log=0
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 安全配置
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 时区配置
default-time-zone='+08:00'

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
