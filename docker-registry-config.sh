#!/bin/bash

# Docker镜像加速器配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

# 配置macOS Docker Desktop镜像加速器
configure_macos() {
    log_step "配置macOS Docker Desktop镜像加速器..."
    
    log_info "请手动配置Docker Desktop镜像加速器："
    echo ""
    echo "1. 打开Docker Desktop"
    echo "2. 点击设置图标 (齿轮图标)"
    echo "3. 选择 'Docker Engine'"
    echo "4. 在JSON配置中添加以下内容："
    echo ""
    echo '{'
    echo '  "registry-mirrors": ['
    echo '    "https://docker.mirrors.ustc.edu.cn",'
    echo '    "https://hub-mirror.c.163.com",'
    echo '    "https://mirror.baidubce.com",'
    echo '    "https://registry.cn-hangzhou.aliyuncs.com"'
    echo '  ],'
    echo '  "insecure-registries": [],'
    echo '  "debug": false,'
    echo '  "experimental": false'
    echo '}'
    echo ""
    echo "5. 点击 'Apply & Restart'"
    echo ""
    log_warn "配置完成后，请重启Docker Desktop"
}

# 配置Linux Docker镜像加速器
configure_linux() {
    log_step "配置Linux Docker镜像加速器..."
    
    # 创建docker配置目录
    sudo mkdir -p /etc/docker
    
    # 创建daemon.json配置文件
    log_info "创建Docker daemon配置文件..."
    sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://registry.cn-hangzhou.aliyuncs.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}
EOF
    
    # 重启Docker服务
    log_info "重启Docker服务..."
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    log_info "Linux Docker镜像加速器配置完成"
}

# 测试镜像拉取
test_pull() {
    log_step "测试镜像拉取..."
    
    log_info "测试拉取Alpine镜像..."
    if docker pull alpine:latest; then
        log_info "镜像拉取测试成功"
        docker rmi alpine:latest
    else
        log_error "镜像拉取测试失败"
        return 1
    fi
}

# 显示可用的镜像源
show_mirrors() {
    log_info "可用的Docker镜像源："
    echo ""
    echo "🇨🇳 国内镜像源："
    echo "  - 中科大镜像: https://docker.mirrors.ustc.edu.cn"
    echo "  - 网易镜像: https://hub-mirror.c.163.com"
    echo "  - 百度镜像: https://mirror.baidubce.com"
    echo "  - 阿里云镜像: https://registry.cn-hangzhou.aliyuncs.com"
    echo "  - 腾讯云镜像: https://mirror.ccs.tencentyun.com"
    echo "  - DaoCloud镜像: http://f1361db2.m.daocloud.io"
    echo ""
    echo "🌍 官方镜像源："
    echo "  - Docker Hub: https://registry-1.docker.io"
    echo ""
    echo "📝 使用方法："
    echo "  在Dockerfile中使用: FROM registry.cn-hangzhou.aliyuncs.com/library/openjdk:8-jdk-alpine"
}

# 主函数
main() {
    case "${1:-help}" in
        "configure")
            OS=$(detect_os)
            case $OS in
                "macos")
                    configure_macos
                    ;;
                "linux")
                    configure_linux
                    ;;
                *)
                    log_error "不支持的操作系统: $OSTYPE"
                    exit 1
                    ;;
            esac
            ;;
        "test")
            test_pull
            ;;
        "mirrors")
            show_mirrors
            ;;
        "help"|*)
            echo "Docker镜像加速器配置脚本"
            echo ""
            echo "使用方法:"
            echo "  $0 configure  - 配置Docker镜像加速器"
            echo "  $0 test       - 测试镜像拉取"
            echo "  $0 mirrors    - 显示可用镜像源"
            echo ""
            echo "示例:"
            echo "  $0 configure  - 自动配置镜像加速器"
            echo "  $0 test       - 测试配置是否生效"
            ;;
    esac
}

# 执行主函数
main "$@"
