package com.chaos.im.c2g.group.domain;

import com.chaos.im.c2g.group.api.domain.LastC2gMessageDTO;
import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * <AUTHOR>
 */
@Getter
@Setter
public class GroupVO extends BaseDomain {

    private Long id;

    /**
     * 群主id
     */
    private String ownerId;

    /**
     * 群名
     */
    private String name;

    private String faceUrl;


    private String ex;

    private List<String> operatorNos;

    private LastMessageVO lastMessage;
}