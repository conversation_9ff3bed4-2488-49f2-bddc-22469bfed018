package com.chaos.im.c2g.group.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chaos.im.c2g.group.api.domain.LastC2gMessageDTO;
import com.chaos.im.c2g.group.api.domain.MessageSendReq;
import com.chaos.im.c2g.group.dao.C2gMessageAckDAO;
import com.chaos.im.c2g.group.domain.C2gMessageAckDO;
import com.chaos.im.c2g.group.domain.C2gMessageBO;
import com.chaos.im.c2g.group.domain.GroupDO;
import com.chaos.im.c2g.group.mapper.C2gMessageAckMapper;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.im.c2g.group.service.GroupService;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.web.util.PageUtil;
import com.outstanding.framework.core.PageInfoDTO;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLiveObjectService;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageSendServiceImpl extends AbstractMessageSendService {

    private static final String ACK_KEY_PREFIX = "IM::C2G_MESSAGE_ACK";

    private static final String ACK_COUNT_KEY_PREFIX = "IM::C2G_MESSAGE_ACK_COUNT";

    private static final Integer ACK_THRESHOLD = 5;

    private static final String GROUP_KEY_PREFIX = "GROUP";

    private static final int FACTOR = 128;

    @Autowired
    private C2gMessageAckDAO c2gMessageAckDAO;

    @Autowired
    private GroupMemberService groupMemberService;

    @Autowired
    private GroupService groupService;

    @DubboReference(version = "1.0.0")
    private ChatApi chatApi;

    private final MongoTemplate mongoTemplate;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private C2gMessageAckMapper c2gMessageAckMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    @Qualifier("longRedisTemplate")
    private RedisTemplate<String,Long> longRedisTemplate;

    @Value("#{'topic-c2g-message-send-'.concat('${spring.profiles.active}')}")
    private String TOPIC_C2G_MESSAGE_SEND;

    @Value("#{'topic-c2c-message-send-'.concat('${spring.profiles.active}')}")
    private String TOPIC_C2C_MESSAGE_SEND;

    @Override
    public void save(List<MessageSendJsonRequest> requests, Long chatId) {

        log.info("接收到kafka群聊消息,准备落库,chatId:{}", chatId);
        List<?> memberIds = redisTemplate.opsForList().range(GROUP_KEY_PREFIX + "::" + chatId, 0, -1);
        log.info("get memberIds from redis by chatId :{}", memberIds);
        String groupId = chatApi.getPeerIdByChatId(chatId);
        log.info("根据chatId:{} 获取groupId:{}", chatId, groupId);
        if (CollectionUtils.isEmpty(memberIds) && StringUtils.isNotEmpty(groupId)) {

            memberIds = groupMemberService.getByGroupId(Long.parseLong(groupId));
            log.info("get memberIds from redis by groupId : {}", memberIds);
        }

//        HashOperations<String, Object, Object> hashOps = redisTemplate.opsForHash();

        String lastMessageKey = ImConstants.C2G_LAST_MESSAGE_PREFIX + chatId;

        for (MessageSendJsonRequest request : requests) {

            C2gMessageBO c2gMessageBO = new C2gMessageBO();
            c2gMessageBO.setId(request.getMessageId());
            c2gMessageBO.setCategory(request.getCategory());
            c2gMessageBO.setGroupId(Long.parseLong(groupId));
            c2gMessageBO.setAppId(request.getToAppId());
            c2gMessageBO.setFromOperatorNo(request.getFromOperatorNo());
            c2gMessageBO.setContent(request.getContent());
            c2gMessageBO.setChatId(request.getChatId());
            c2gMessageBO.setMessageId(request.getChatId() + "-" + request.getMessageId());
            c2gMessageBO.setSequence(request.getSequence());
            c2gMessageBO.setTimestamp(System.currentTimeMillis());

//            RLiveObjectService service =  redissonClient.getLiveObjectService();
            Map<String,Object> map =  BeanUtil.beanToMap(c2gMessageBO);
            log.info("记录最后一条c2cMessageBo:{}",JSONUtil.toJsonStr(map));
            if(map!=null) {
                Map<String, Object> filteredMap = map.entrySet().stream()
                        .filter(entry -> entry.getValue() != null)
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue
                        ));
                redissonClient.getMap(lastMessageKey).putAll(filteredMap);
            }


            if (memberIds != null) {
                for (String memberId : (List<String>) memberIds) {
                    if(!Objects.equals(memberId,request.getFromOperatorNo())) {
                        String unReadCountKey = ImConstants.C2G_UNREAD_COUNT_PREFIX + chatId + "::" + memberId;
                        redissonClient.getAtomicLong(unReadCountKey).incrementAndGet();
                    }
                }
            }
            mongoTemplate.save(c2gMessageBO);
            sendMessageSendResponse(request);
        }
        GroupDO groupDO = groupService.getById(groupId);
        if (groupDO != null) {
            for (MessageSendJsonRequest request : requests) {
                sendMessagePush(request, (List<String>) memberIds, Long.parseLong(groupId));
            }
        } else {
            log.info("group had been deleted,groupId:{}", groupId);
        }
    }

    @Override
    public void updateAck(Long chatId, String memberId, String deviceId, Long messageId, Long sequence) {

        long count = c2gMessageAckDAO.count(chatId, memberId, deviceId);
        if (count == 0) {
            C2gMessageAckDO c2gMessageAckDO = new C2gMessageAckDO();
            c2gMessageAckDO.setChatId(chatId);
            c2gMessageAckDO.setMemberId(memberId);
            c2gMessageAckDO.setDeviceId(deviceId);
            c2gMessageAckDO.setLastAckMessageId(messageId);
            c2gMessageAckDO.setSequence(sequence);
            c2gMessageAckDAO.save(c2gMessageAckDO);
        } else {
            String unReadCountKey =ImConstants.C2G_UNREAD_COUNT_PREFIX + chatId + ImConstants.SEPARATOR + memberId;
            redissonClient.getAtomicLong(unReadCountKey).set(0);
            c2gMessageAckDAO.updateAck(chatId, memberId, deviceId, messageId, sequence);
        }
    }

    @Override
    public PageInfoDTO<MessageJsonPush> fetch(Long chatId, String operatorNo, String deviceId, Long startMessageId,
                                              Long stopMessageId, int size, int current, Map<String, String> sort) {

        Criteria criteria = Criteria.where("chatId").is(chatId);

        if (startMessageId != null && stopMessageId != null) {
            criteria.and("_id").gt(startMessageId).lte(stopMessageId);
        } else if (startMessageId != null && stopMessageId == null) {
            criteria.and("_id").gt(startMessageId);
        } else if (startMessageId == null && stopMessageId != null) {
            criteria.and("_id").lte(stopMessageId);
        }

        // 构建查询对象
        Query query = new Query(criteria);
        if (Objects.nonNull(sort)) {
            sort.entrySet().stream().forEach(it -> {
                if ("DESC".equals(it.getValue())) {
                    query.with(Sort.by(Sort.Direction.DESC, it.getKey()));
                } else {
                    query.with(Sort.by(Sort.Direction.ASC, it.getKey()));
                }
            });
        }

        // 分页处理
        Pageable page = PageRequest.of(current - 1, size); // 假设从第一页开始
        query.with(page);

        // 执行查询
        List<C2gMessageBO> messageBOList = mongoTemplate.find(query, C2gMessageBO.class);

        long totalCount = mongoTemplate.count(new Query(criteria), C2gMessageBO.class);

        // 将 C2gMessageBO 转换为 MessageJsonPush
        List<MessageJsonPush> messageJsonPushList = messageBOList.stream()
                .map(bo -> convertToMessageJsonPush(bo, operatorNo))
                .collect(Collectors.toList());

        return PageUtil.createPage(current, size, totalCount, messageJsonPushList);

    }

    @Override
    public Long lastMessageAck(Long chatId, String operatorNo, String deviceId) {
        String businessKey = chatId + "::" + operatorNo + "::" + deviceId;
        // 组装redis key
        String key = ACK_KEY_PREFIX + "::" + businessKey;
        // 获取缓存在redis的ackId
        Optional<Object> ackOptional = Optional.ofNullable(redisTemplate.opsForValue().get(key));
        Long storeAckId = (Long) ackOptional.orElse(0L);
        if (storeAckId == 0) {
            return c2gMessageAckDAO.getLastAckMessageId(chatId, operatorNo, deviceId);
        } else {
            return storeAckId;
        }
    }

    private MessageJsonPush convertToMessageJsonPush(C2gMessageBO c2gMessageBO, String memberId) {
        MessageJsonPush messageJsonPush = new MessageJsonPush();
        messageJsonPush.setMessageId(c2gMessageBO.getId());
        messageJsonPush.setCategory(c2gMessageBO.getCategory());
        messageJsonPush.setFromOperatorNo(c2gMessageBO.getFromOperatorNo());
        messageJsonPush.setContent(c2gMessageBO.getContent());
        messageJsonPush.setChatId(c2gMessageBO.getChatId());
        messageJsonPush.setToOperatorNo(memberId);
        messageJsonPush.setChatType(Constants.CHAT_TYPE_C2G);
        messageJsonPush.setTimestamp(c2gMessageBO.getTimestamp());
        messageJsonPush.setSequence(c2gMessageBO.getSequence());
        return messageJsonPush;
    }

    private String getRowKey(Long groupId, Long messageId) {
        Long tempMessageId = messageId;
        if (Objects.isNull(tempMessageId)) {
            tempMessageId = 0L;
        }
        // 逆序消息id，做排序用，最新的消息-先查找
        return getRowKey(groupId)
                + StringUtils.leftPad(String.valueOf(Long.MAX_VALUE - tempMessageId), 19, '0');
    }

    private String getRowKey(Long groupId) {
        int hash = (int) (groupId % FACTOR);
        // 3位群会话id的hash+群id+逆序消息id
        // hash，是为了在hbase中分区存储，相当于mysql的分表
        // 群会话id，rowKey做群消息查询
        return StringUtils.leftPad(String.valueOf(hash), 3, '0') + "|"
                + StringUtils.leftPad(String.valueOf(groupId), 19, '0') + "|";
    }

    @Override
    public void sendMessage(MessageSendReq req) {
        MessageSendJsonRequest request = new MessageSendJsonRequest();
        request.setMessageId(req.getMessageId());
        request.setFromOperatorNo(req.getFromOperatorNo());
        request.setChatId(req.getChatId());
        request.setChatType(req.getChatType());
        request.setContent(req.getContent());
        request.setCategory(req.getCategory());
        // 使用 redis 获取当前服务的 sequence
        Long chatId = request.getChatId();
        Optional<Long> sequenceOptional = Optional.ofNullable(redisTemplate
                .boundValueOps(com.chaos.keep.alive.common.im.constant.Constants.REDIS_SEQ_KEY + "::" + chatId)
                .increment());
        // 群聊服务端设置sequence
        request.setSequence(sequenceOptional.orElse(1L));
        kafkaTemplate.send(TOPIC_C2G_MESSAGE_SEND, String.valueOf(request.getChatId()),
                JSONUtil.toJsonStr(request));
        log.info(JSONUtil.toJsonStr(request));
    }

    @Override
    public LastC2gMessageDTO getLastMessageByChatIdAndOperatorNo(Long chatId, String operatorNo, String deviceId) {

        LastC2gMessageDTO lastC2cMessageDTO = new LastC2gMessageDTO();

        StopWatch lastMsgStopWatch = new StopWatch();
        lastMsgStopWatch.start("getC2gLastMessage chatId:" + chatId+" operatorNo:"+operatorNo);
        C2gMessageBO c2gMessageBO = getLastC2gMessage(chatId, lastC2cMessageDTO);
        if (c2gMessageBO == null) return null;

        lastMsgStopWatch.stop();
        log.info(lastMsgStopWatch.prettyPrint());

        StopWatch unReadCountStopWatch = new StopWatch();
        unReadCountStopWatch.start("setUnReadCount chatId:" + chatId+" operatorNo:"+operatorNo);

        setUnReadCount(chatId, operatorNo, deviceId, c2gMessageBO, lastC2cMessageDTO);

        unReadCountStopWatch.stop();
        log.info(unReadCountStopWatch.prettyPrint());

        return lastC2cMessageDTO;
    }

    private void setUnReadCount(Long chatId, String operatorNo, String deviceId, C2gMessageBO c2gMessageBO, LastC2gMessageDTO lastC2cMessageDTO) {
        String unReadCountKey = ImConstants.C2G_UNREAD_COUNT_PREFIX  + chatId + ImConstants.SEPARATOR + operatorNo;
        QueryWrapper<C2gMessageAckDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("chat_id", chatId);
        queryWrapper.eq("member_id", operatorNo);
        queryWrapper.eq("device_id", deviceId);

        C2gMessageAckDO c2gMessageAckDO = c2gMessageAckMapper.selectOne(queryWrapper);
        if (c2gMessageAckDO != null && c2gMessageBO != null) {

            lastC2cMessageDTO.setUnReadCount(c2gMessageBO.getSequence() - c2gMessageAckDO.getSequence());

        } else {
            Long count =  redissonClient.getAtomicLong(unReadCountKey).get();
            lastC2cMessageDTO.setUnReadCount(count);
        }
    }

    private C2gMessageBO getLastC2gMessage(Long chatId, LastC2gMessageDTO lastC2cMessageDTO) {
        String lastMessageKey = ImConstants.C2G_LAST_MESSAGE_PREFIX  + chatId;

        C2gMessageBO c2gMessageBO = null;
        RMap<Object,Object> hashObject = redissonClient.getMap(lastMessageKey);
        log.info("get lastMessage:[{}] key:[{}] from redis",JSONUtil.toJsonStr(hashObject),lastMessageKey);
        if (hashObject != null) {
            c2gMessageBO = BeanUtil.mapToBean(hashObject, C2gMessageBO.class, true);
        } else {
        }


        if (c2gMessageBO != null) {
            BeanUtils.copyProperties(c2gMessageBO, lastC2cMessageDTO);
            lastC2cMessageDTO.setMessageId(c2gMessageBO.getId());
        } else {
            return null;
        }
        return c2gMessageBO;
    }
}
