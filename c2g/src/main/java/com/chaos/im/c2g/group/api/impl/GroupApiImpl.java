package com.chaos.im.c2g.group.api.impl;

import com.chaos.im.c2g.group.api.GroupApi;
import com.chaos.im.c2g.group.api.domain.CreateGroupAndInviteReq;
import com.chaos.im.c2g.group.api.domain.GroupDTO;
import com.chaos.im.c2g.group.api.domain.LastC2gMessageDTO;
import com.chaos.im.c2g.group.domain.*;
import com.chaos.im.c2g.group.mapper.C2gMessageAckMapper;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.im.c2g.group.service.GroupService;
import com.chaos.im.c2g.group.service.MessageSendService;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.chaos.keep.alive.common.core.util.ResultHelper;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.outstanding.framework.core.ResponseDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@DubboService(version = "1.0.0", interfaceClass = GroupApi.class)
@Slf4j
public class GroupApiImpl implements GroupApi {

    @Resource
    private GroupMemberService groupMemberService;

    @Resource
    private GroupService groupService;

    private final MongoTemplate mongoTemplate;

    private final C2gMessageAckMapper c2gMessageAckMapper;

    @Resource
    private MessageSendService messageSendService;

    @DubboReference(version="1.0.0")
    private ChatApi chatApi;


    private static final String EX_CUSTOMER_SERVICE_STATUS = "customerServiceStatus";

    @Override
    public List<String> getMemberIdByGroupId(Long groupId) {
        return groupMemberService.getByGroupId(groupId);
    }

    @Override
    public ResponseDTO<?> create(String ownerId, String name) {

        GroupVO groupVO = new GroupVO();
        groupVO.setOwnerId(ownerId);
        groupVO.setName(name);

        groupService.create(groupVO.clone(GroupDTO.class));
        return ResultHelper.ok();
    }

    @Override
    public ResponseDTO<?> dismiss(Long groupId) {
        groupService.dismiss(groupId);
        chatApi.deleteChatByGroupId(Constants.CHAT_TYPE_C2G, groupId);
        return ResultHelper.ok();
    }

    @Override
    @Transactional
    public ResponseDTO<Long> createAndInvite(CreateGroupAndInviteReq req) {
        return groupService.createAndInvite(req.getGroup(),req.getMemberOperatorNos());
    }

    @Override
    public ResponseDTO<?> findGroupByGroupId(Long groupId) {
       return ResultHelper.ok(groupService.findByGroupId(groupId));
    }

    @Override
    public ResponseDTO<?> kick(Long groupId, List<String> memberOperatorNos) {

        log.info("dubbo调用踢出群聊 groupId:{}, memberOperatorNos:{}", groupId, memberOperatorNos);
        for(String memberId:memberOperatorNos) {
            groupMemberService.kick(groupId, memberId);
            chatApi.deleteChatByGroupId(Constants.CHAT_TYPE_C2G, groupId, memberId);
        }

        return ResultHelper.ok();

    }

    @Override
    @Transactional
    public ResponseDTO<?> invite(Long groupId, List<String> memberOperatorNos) {

        for(String memberId:memberOperatorNos) {
            groupMemberService.join(groupId, memberId);
        }
        return ResultHelper.ok();

    }

    @Override
    public ResponseDTO<?> findMemberByGroupId(Long groupId, Integer pageSize, Integer current) {

        GroupMemberQuery query = new GroupMemberQuery();
        query.setGroupId(groupId);
        query.setCurrent(current);
        query.setPageSize(pageSize);

        return ResultHelper.ok(BeanCopierUtils.convert(groupMemberService.listGroupMember(query), GroupMemberVO.class));

    }

    @Override
    public LastC2gMessageDTO getLastMessageByChatIdAndOperatorNo(Long chatId, String operatorNo,String deviceId) {
        return messageSendService.getLastMessageByChatIdAndOperatorNo(chatId, operatorNo, deviceId);
    }
}
