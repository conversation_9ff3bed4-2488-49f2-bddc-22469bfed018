package com.chaos.im.c2g.group.controller;

import com.chaos.im.c2g.group.domain.*;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.outstanding.framework.core.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/group/member")
public class GroupMemberController {

    @Autowired
    private GroupMemberService groupMemberService;

    @PostMapping("/page")
    public ResponseDTO<?> listGroupMember(@RequestBody GroupMemberQuery query) {
        return ResponseDTO.creatDTO(BeanCopierUtils.convert(groupMemberService.listGroupMember(query), GroupMemberVO.class));
    }

    @PostMapping("/join")
    public ResponseDTO<?> join(@RequestBody GroupMemberJoinVO groupMemberJoinVO) {
        for(String memberId:groupMemberJoinVO.getMemberIds()) {
            groupMemberService.join(groupMemberJoinVO.getGroupId(), memberId);
        }
        return ResponseDTO.creatDTO();
    }


    @PostMapping("/kick")
    public ResponseDTO<?> kick(@RequestBody GroupMemberKickVO groupMemberKickVO) {
        for(String memberId:groupMemberKickVO.getMemberIds()) {
            groupMemberService.kick(groupMemberKickVO.getGroupId(), memberId);
        }

        return ResponseDTO.creatDTO();
    }
}
