package com.chaos.im.c2g.group.api.impl;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.chaos.im.c2g.group.api.C2gOpenApi;
import com.chaos.im.c2g.group.api.domain.MessageSendReq;
import com.chaos.im.c2g.group.service.MessageSendService;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@DubboService(version = "1.0.0", interfaceClass = C2gOpenApi.class)
public class C2gOpenApiImpl implements C2gOpenApi{

    @Autowired
    private MessageSendService messageSendService;

    @Override
    public void sendMessage(MessageSendReq req) {
        messageSendService.sendMessage(req);
    }
    

    
}
