package com.chaos.im.c2g.group.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaos.im.c2g.group.dao.GroupMemberDAO;
import com.chaos.im.c2g.group.domain.GroupMemberDO;
import com.chaos.im.c2g.group.domain.GroupMemberQuery;
import com.chaos.im.c2g.group.mapper.GroupMemberMapper;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.web.dao.MybatisPlusDAOImpl;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class GroupMemberDAOImpl extends MybatisPlusDAOImpl<GroupMemberMapper, GroupMemberDO> implements GroupMemberDAO {

    @Override
    public BasePage<GroupMemberDO> listByPage(GroupMemberQuery query) {
        LambdaQueryWrapper<GroupMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupMemberDO::getGroupId, query.getGroupId());
        return listPage(query, queryWrapper);
    }

    @Override
    public long count(Long groupId, String memberId) {
        LambdaQueryWrapper<GroupMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupMemberDO::getGroupId, groupId);
        queryWrapper.eq(GroupMemberDO::getMemberId, memberId);
        return mapper.selectCount(queryWrapper);
    }

    @Override
    public List<String> getByGroupId(Long groupId) {
        LambdaQueryWrapper<GroupMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(GroupMemberDO::getMemberId);
        queryWrapper.eq(GroupMemberDO::getGroupId, groupId);
        return mapper.selectList(queryWrapper).stream().map(GroupMemberDO::getMemberId).collect(Collectors.toList());
    }


    @Override
    public BasePage<String> listGroupMemberByPage(GroupMemberQuery query, String memberId) {
        IPage<String> page = new Page<>(query.getCurrent(), query.getPageSize());
        page.setRecords(mapper.list(page, memberId));
        return new BasePage<>(page.getRecords(), page.getTotal(), page.getSize(), page.getCurrent());
    }

    @Override
    public long count(Long groupId) {
        LambdaQueryWrapper<GroupMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupMemberDO::getGroupId, groupId);
        return mapper.selectCount(queryWrapper);
    }

    @Override
    public void deleteByGroupId(Long groupId) {
        LambdaQueryWrapper<GroupMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupMemberDO::getGroupId, groupId);
        mapper.delete(queryWrapper);
    }
}
