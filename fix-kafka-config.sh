#!/bin/bash

# Kafka配置修复脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Kafka配置
check_kafka_config() {
    log_step "检查Kafka配置..."
    
    local files=("docker-compose.yml" "docker-compose.dev.yml")
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            log_info "检查文件: $file"
            
            if grep -q "KAFKA_LISTENERS:" "$file"; then
                log_info "  ✅ KAFKA_LISTENERS 已配置"
            else
                log_warn "  ❌ KAFKA_LISTENERS 未配置"
            fi
            
            if grep -q "KAFKA_ADVERTISED_LISTENERS:" "$file"; then
                log_info "  ✅ KAFKA_ADVERTISED_LISTENERS 已配置"
            else
                log_warn "  ❌ KAFKA_ADVERTISED_LISTENERS 未配置"
            fi
            
            echo ""
        else
            log_warn "文件不存在: $file"
        fi
    done
}

# 测试Kafka启动
test_kafka_startup() {
    log_step "测试Kafka启动..."
    
    # 停止现有的Kafka容器
    log_info "停止现有的Kafka和Zookeeper容器..."
    docker-compose -f docker-compose.dev.yml stop kafka zookeeper 2>/dev/null || true
    docker-compose -f docker-compose.dev.yml rm -f kafka zookeeper 2>/dev/null || true
    
    # 启动Zookeeper
    log_info "启动Zookeeper..."
    docker-compose -f docker-compose.dev.yml up -d zookeeper
    
    # 等待Zookeeper启动
    log_info "等待Zookeeper启动完成..."
    sleep 10
    
    # 启动Kafka
    log_info "启动Kafka..."
    docker-compose -f docker-compose.dev.yml up -d kafka
    
    # 等待Kafka启动
    log_info "等待Kafka启动完成..."
    sleep 15
    
    # 检查Kafka状态
    if docker-compose -f docker-compose.dev.yml ps kafka | grep -q "Up"; then
        log_info "✅ Kafka启动成功"
        
        # 测试Kafka连接
        log_info "测试Kafka连接..."
        if docker-compose -f docker-compose.dev.yml exec -T kafka kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1; then
            log_info "✅ Kafka连接测试成功"
        else
            log_warn "⚠️ Kafka连接测试失败，但容器已启动"
        fi
    else
        log_error "❌ Kafka启动失败"
        log_info "查看Kafka日志:"
        docker-compose -f docker-compose.dev.yml logs kafka
        return 1
    fi
}

# 创建测试主题
create_test_topic() {
    log_step "创建测试主题..."
    
    local topic_name="test-topic"
    
    if docker-compose -f docker-compose.dev.yml exec -T kafka kafka-topics \
        --bootstrap-server localhost:9092 \
        --create \
        --topic "$topic_name" \
        --partitions 1 \
        --replication-factor 1 > /dev/null 2>&1; then
        log_info "✅ 测试主题创建成功: $topic_name"
        
        # 列出主题
        log_info "当前主题列表:"
        docker-compose -f docker-compose.dev.yml exec -T kafka kafka-topics \
            --bootstrap-server localhost:9092 \
            --list
            
        # 删除测试主题
        docker-compose -f docker-compose.dev.yml exec -T kafka kafka-topics \
            --bootstrap-server localhost:9092 \
            --delete \
            --topic "$topic_name" > /dev/null 2>&1
        log_info "✅ 测试主题已删除"
    else
        log_error "❌ 测试主题创建失败"
        return 1
    fi
}

# 显示Kafka配置说明
show_kafka_config_info() {
    log_step "Kafka配置说明..."
    
    echo ""
    echo "🔧 Kafka环境变量说明:"
    echo ""
    echo "必需的环境变量:"
    echo "  KAFKA_LISTENERS: Kafka监听的地址和端口"
    echo "    - 格式: PROTOCOL://HOST:PORT"
    echo "    - 示例: PLAINTEXT://0.0.0.0:9092"
    echo ""
    echo "  KAFKA_ADVERTISED_LISTENERS: 客户端连接的地址"
    echo "    - 格式: PROTOCOL://HOST:PORT"
    echo "    - 示例: PLAINTEXT://kafka:9092"
    echo ""
    echo "  KAFKA_ZOOKEEPER_CONNECT: Zookeeper连接地址"
    echo "    - 示例: zookeeper:2181"
    echo ""
    echo "可选的环境变量:"
    echo "  KAFKA_BROKER_ID: Broker ID (默认: 1)"
    echo "  KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 偏移量主题副本因子"
    echo "  KAFKA_AUTO_CREATE_TOPICS_ENABLE: 自动创建主题"
    echo ""
    echo "🌐 网络配置:"
    echo "  内部通信: kafka:9092 (容器间通信)"
    echo "  外部访问: localhost:29092 (主机访问)"
    echo ""
    echo "📋 常用命令:"
    echo "  # 列出主题"
    echo "  docker-compose -f docker-compose.dev.yml exec kafka kafka-topics --bootstrap-server localhost:9092 --list"
    echo ""
    echo "  # 创建主题"
    echo "  docker-compose -f docker-compose.dev.yml exec kafka kafka-topics --bootstrap-server localhost:9092 --create --topic my-topic --partitions 1 --replication-factor 1"
    echo ""
    echo "  # 查看Kafka日志"
    echo "  docker-compose -f docker-compose.dev.yml logs kafka"
}

# 清理Kafka容器
cleanup_kafka() {
    log_step "清理Kafka容器..."
    
    log_info "停止Kafka和Zookeeper容器..."
    docker-compose -f docker-compose.dev.yml stop kafka zookeeper 2>/dev/null || true
    
    log_info "删除Kafka和Zookeeper容器..."
    docker-compose -f docker-compose.dev.yml rm -f kafka zookeeper 2>/dev/null || true
    
    log_info "✅ Kafka容器清理完成"
}

# 显示帮助信息
show_help() {
    echo "Kafka配置修复脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 check     - 检查Kafka配置"
    echo "  $0 test      - 测试Kafka启动"
    echo "  $0 topic     - 创建测试主题"
    echo "  $0 info      - 显示配置说明"
    echo "  $0 cleanup   - 清理Kafka容器"
    echo "  $0 fix       - 自动修复并测试"
    echo ""
    echo "示例:"
    echo "  $0 fix       - 自动修复Kafka配置并测试"
    echo "  $0 test      - 测试Kafka是否能正常启动"
}

# 自动修复
auto_fix() {
    log_step "开始自动修复Kafka配置..."
    
    # 1. 检查配置
    check_kafka_config
    
    # 2. 测试启动
    if test_kafka_startup; then
        # 3. 创建测试主题
        create_test_topic
        
        log_info "🎉 Kafka配置修复成功！"
        show_kafka_config_info
    else
        log_error "❌ Kafka配置修复失败"
        log_info "请检查Docker日志获取更多信息"
        return 1
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_kafka_config
            ;;
        "test")
            test_kafka_startup
            ;;
        "topic")
            create_test_topic
            ;;
        "info")
            show_kafka_config_info
            ;;
        "cleanup")
            cleanup_kafka
            ;;
        "fix")
            auto_fix
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
