#!/bin/bash

# 编译性能测试脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GRE<PERSON>}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 测试编译性能
benchmark_compile() {
    local module=${1:-"c2c"}
    
    log_step "测试${module}模块编译性能..."
    
    # 清理缓存
    rm -rf "${module}/target"
    rm -f "${module}/.dep-cache"
    rm -rf ".dev-cache"
    
    echo ""
    echo "🧪 测试场景:"
    echo "  模块: $module"
    echo "  测试次数: 3次"
    echo "  测试内容: 完整编译 + 依赖复制"
    echo ""
    
    # 测试3次完整编译
    local total_time=0
    for i in {1..3}; do
        log_info "第${i}次测试..."
        
        local start_time=$(date +%s)
        
        # 执行编译
        ./dev-hot-reload.sh compile "$module" > /dev/null 2>&1
        
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        total_time=$((total_time + duration))
        
        echo "  第${i}次: ${duration}秒"
        
        # 清理（除了第一次，测试缓存效果）
        if [ $i -eq 1 ]; then
            log_info "保留缓存，测试增量编译..."
        fi
    done
    
    local avg_time=$((total_time / 3))
    
    echo ""
    echo "📊 测试结果:"
    echo "  总耗时: ${total_time}秒"
    echo "  平均耗时: ${avg_time}秒"
    echo "  首次编译: 包含依赖下载和缓存初始化"
    echo "  后续编译: 利用缓存，应该更快"
}

# 测试快速重载性能
benchmark_fast_reload() {
    local module=${1:-"c2c"}
    
    log_step "测试${module}模块快速重载性能..."
    
    # 确保已编译
    ./dev-hot-reload.sh compile "$module" > /dev/null 2>&1
    
    echo ""
    echo "🚀 快速重载测试:"
    echo "  模块: $module"
    echo "  测试次数: 5次"
    echo "  测试内容: 仅编译Java文件"
    echo ""
    
    local total_time=0
    for i in {1..5}; do
        log_info "第${i}次快速重载测试..."
        
        local start_time=$(date +%s)
        
        # 执行快速编译（仅Java文件）
        mvn compile -pl "$module" -am -q -T 1C > /dev/null 2>&1
        
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        total_time=$((total_time + duration))
        
        echo "  第${i}次: ${duration}秒"
    done
    
    local avg_time=$((total_time / 5))
    
    echo ""
    echo "⚡ 快速重载结果:"
    echo "  总耗时: ${total_time}秒"
    echo "  平均耗时: ${avg_time}秒"
    echo "  适用场景: 只修改Java代码，不涉及依赖变化"
}

# 比较不同编译方式的性能
compare_methods() {
    local module=${1:-"c2c"}
    
    log_step "比较不同编译方式的性能..."
    
    echo ""
    echo "🔄 性能对比测试:"
    echo ""
    
    # 清理环境
    rm -rf "${module}/target"
    rm -f "${module}/.dep-cache"
    rm -rf ".dev-cache"
    
    # 1. 传统方式（每次都复制依赖）
    log_info "1. 传统方式测试..."
    local start1=$(date +%s)
    mvn compile -pl "$module" -am -q > /dev/null 2>&1
    mvn dependency:copy-dependencies -DoutputDirectory="${module}/target/lib" -pl "$module" -q > /dev/null 2>&1
    local end1=$(date +%s)
    local time1=$((end1 - start1))
    echo "   传统方式: ${time1}秒"
    
    # 清理
    rm -rf "${module}/target"
    
    # 2. 优化方式（使用缓存）
    log_info "2. 优化方式测试..."
    local start2=$(date +%s)
    ./dev-hot-reload.sh compile "$module" > /dev/null 2>&1
    local end2=$(date +%s)
    local time2=$((end2 - start2))
    echo "   优化方式: ${time2}秒"
    
    # 3. 快速方式（仅Java）
    log_info "3. 快速方式测试..."
    local start3=$(date +%s)
    mvn compile -pl "$module" -am -q -T 1C > /dev/null 2>&1
    local end3=$(date +%s)
    local time3=$((end3 - start3))
    echo "   快速方式: ${time3}秒"
    
    echo ""
    echo "📈 性能对比结果:"
    echo "  传统方式: ${time1}秒 (基准)"
    echo "  优化方式: ${time2}秒 (节省: $((time1 - time2))秒)"
    echo "  快速方式: ${time3}秒 (节省: $((time1 - time3))秒)"
    
    if [ $time2 -lt $time1 ]; then
        local improvement=$(( (time1 - time2) * 100 / time1 ))
        echo "  优化效果: 提升 ${improvement}%"
    fi
}

# 显示缓存统计
show_cache_stats() {
    log_step "显示缓存统计信息..."
    
    echo ""
    echo "💾 缓存统计:"
    
    # 全局缓存
    if [ -d ".dev-cache/lib" ]; then
        local cache_size=$(du -sh .dev-cache/lib 2>/dev/null | cut -f1)
        local jar_count=$(find .dev-cache/lib -name "*.jar" 2>/dev/null | wc -l)
        echo "  全局缓存: $cache_size ($jar_count 个JAR文件)"
    else
        echo "  全局缓存: 未初始化"
    fi
    
    # 模块缓存
    echo "  模块缓存:"
    for module in c2c c2g chat gateway-tcp route id-server; do
        if [ -d "${module}/target/lib" ]; then
            local module_size=$(du -sh "${module}/target/lib" 2>/dev/null | cut -f1)
            echo "    ${module}: $module_size"
        else
            echo "    ${module}: 未缓存"
        fi
    done
    
    # Maven本地仓库
    if [ -d "$HOME/.m2/repository" ]; then
        local m2_size=$(du -sh "$HOME/.m2/repository" 2>/dev/null | cut -f1)
        echo "  Maven仓库: $m2_size"
    fi
}

# 显示帮助信息
show_help() {
    echo "编译性能测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 compile [module]  - 测试编译性能"
    echo "  $0 fast [module]     - 测试快速重载性能"
    echo "  $0 compare [module]  - 比较不同编译方式"
    echo "  $0 stats             - 显示缓存统计"
    echo "  $0 all [module]      - 运行所有测试"
    echo ""
    echo "示例:"
    echo "  $0 all c2c          - 对C2C模块运行所有性能测试"
    echo "  $0 compare          - 比较不同编译方式（默认C2C模块）"
}

# 运行所有测试
run_all_tests() {
    local module=${1:-"c2c"}
    
    log_step "运行所有性能测试..."
    
    benchmark_compile "$module"
    echo ""
    benchmark_fast_reload "$module"
    echo ""
    compare_methods "$module"
    echo ""
    show_cache_stats
}

# 主函数
main() {
    case "${1:-help}" in
        "compile")
            benchmark_compile "$2"
            ;;
        "fast")
            benchmark_fast_reload "$2"
            ;;
        "compare")
            compare_methods "$2"
            ;;
        "stats")
            show_cache_stats
            ;;
        "all")
            run_all_tests "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
