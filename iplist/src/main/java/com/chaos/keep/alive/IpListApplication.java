package com.chaos.keep.alive;

import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.chaos.keep.alive.common.web.config.GlobalExceptionConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Import;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@Import({FastJsonConfig.class, GlobalExceptionConfig.class})
public class IpListApplication {

    public static void main(String[] args) {
        SpringApplication.run(IpListApplication.class, args);
    }
}
