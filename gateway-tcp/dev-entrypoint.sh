#!/bin/bash

echo "=== 开发环境热启动模式 ==="
echo "远程调试端口: 5005"
echo "应用端口: 8094"
echo "Dubbo端口: 20886"
echo "=========================="

# 等待依赖服务启动
echo "等待依赖服务启动..."
sleep 10

# 检查并创建lib目录
if [ ! -d "/app/lib" ]; then
    echo "创建lib目录..."
    mkdir -p /app/lib
fi

# 如果lib目录为空，尝试从挂载的target目录复制依赖
if [ -d "/app/target/lib" ] && [ "$(ls -A /app/target/lib 2>/dev/null)" ]; then
    echo "从target目录复制依赖..."
    cp /app/target/lib/*.jar /app/lib/ 2>/dev/null || true
fi

# 检查是否有预编译的jar包
if [ -f "/app/target/app.jar" ]; then
    echo "使用预编译的jar包启动..."
    exec java $JAVA_OPTS -jar /app/target/app.jar
else
    echo "使用class文件启动..."
    # 构建classpath
    CLASSPATH="/app/target/classes"
    for jar in /app/lib/*.jar; do
        CLASSPATH="$CLASSPATH:$jar"
    done
    
    # 查找主类
    MAIN_CLASS=$(find /app/target/classes -name "*Application.class" | head -1 | sed 's|/app/target/classes/||' | sed 's|/|.|g' | sed 's|.class||')
    
    if [ -z "$MAIN_CLASS" ]; then
        echo "错误: 找不到主类，请确保已编译"
        exit 1
    fi
    
    echo "启动主类: $MAIN_CLASS"
    echo "Classpath: $CLASSPATH"
    
    exec java $JAVA_OPTS -cp "$CLASSPATH" "$MAIN_CLASS"
fi
