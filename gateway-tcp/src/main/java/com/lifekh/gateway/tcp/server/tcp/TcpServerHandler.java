package com.lifekh.gateway.tcp.server.tcp;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import com.lifekh.gateway.tcp.client.ClientManager;
import com.lifekh.gateway.tcp.properties.ConfigProperties;
import com.lifekh.gateway.tcp.server.command.ServerCommandHandler;
import com.lifekh.gateway.tcp.server.command.ServerCommandHandlerFactory;
import com.lifekh.gateway.tcp.util.GatewayContext;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.DecoderException;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.SocketAddress;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ChannelHandler.Sharable
public class TcpServerHandler extends SimpleChannelInboundHandler<Command> {

    private final ServerCommandHandlerFactory serverCommandHandlerFactory;

    private final ClientManager clientManager;

    private final GatewayContext context;

    private final ConfigProperties configProperties;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        // 控制在线客户端数量，保证服务稳定
        if (context.onlineCount() > configProperties.getMaxOnlineCount()) {
            log.info("客户端连接已满，断开连接");
            ctx.close();
        } else {
            log.info("客户端连接已建立:{}", ctx);
            super.channelActive(ctx);
        }
    }

    /**
     * 客户端连接断开
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        Optional<ClientManager.ClientInstance> optional = clientManager.getClientId((SocketChannel) ctx.channel());
        ClientManager.ClientInstance instance = optional.orElse(new ClientManager.ClientInstance(null, null));
        log.info("客户端连接断开 :{}", instance.clientId());
        offline(ctx);
    }


    @SneakyThrows
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, Command command) {
        int type = command.getBizType();
        if (type == CommandType.COMMAND_HEARTBEAT) {
            Optional<ClientManager.ClientInstance> optional = clientManager.getClientId((SocketChannel) ctx.channel());
            ClientManager.ClientInstance instance = optional.orElse(new ClientManager.ClientInstance(null, null));
            log.trace("收到客户端消息，客户端:{},业务类型：{}", command.getOperatorNo() + "-" + command.getDeviceId(), type);
        }else{
            Optional<ClientManager.ClientInstance> optional = clientManager.getClientId((SocketChannel) ctx.channel());
            ClientManager.ClientInstance instance = optional.orElse(new ClientManager.ClientInstance(null, null));
            log.debug("收到客户端消息，客户端:{},业务类型：{}", command.getOperatorNo() + "-" + command.getDeviceId(), type);
        }
        ServerCommandHandler commandHandler = serverCommandHandlerFactory.getCommandHandler(type);
        commandHandler.handleCommand(command, ctx);
    }

    /**
     * 客户端心跳检查超时
     */
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent idleStateEvent = (IdleStateEvent) evt;
            if (idleStateEvent.state() == IdleState.READER_IDLE) {
                SocketChannel clientChannel = (SocketChannel) ctx.channel();
                Optional<ClientManager.ClientInstance> optional = clientManager.getClientId(clientChannel);
                optional.ifPresent(clientInstance -> log.info("心跳超时:{}", clientInstance.clientId()));
                offline(ctx);
            }
        }
    }

    private void offline(ChannelHandlerContext ctx) {
        SocketChannel socketChannel = (SocketChannel) ctx.channel();
        Optional<ClientManager.ClientInstance> optional = clientManager.removeChannel(socketChannel);
        optional.ifPresent(clientInstance -> {
            context.offline();
            ServerCommandHandler commandHandler = serverCommandHandlerFactory.getCommandHandler(CommandType.COMMAND_OFFLINE);
            Command command = Command.newBuilder()
                    .setOperatorNo(clientInstance.getOperatorNo())
                    .setDeviceId(clientInstance.getDeviceId())
                    .setBizType(CommandType.COMMAND_OFFLINE)
                    .build();
            commandHandler.handleCommand(command, ctx);
        });

        ctx.close();
        log.info("客户端连接断开,本地长连接移除成功");
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {

        if (cause instanceof DecoderException) {
            SocketAddress socketAddress = ctx.channel().remoteAddress();
            Optional<ClientManager.ClientInstance> optional = clientManager.getClientId((SocketChannel) ctx.channel());
            ClientManager.ClientInstance instance = optional.orElse(new ClientManager.ClientInstance(null, null));
            log.error("客户端发送非法消息：{} 客户端:{},channel:{},socketAddress:{}", cause.getMessage(), instance.clientId(), ctx.channel(), socketAddress, cause);
            try {
                offline(ctx);
            }catch (Exception e) {
                log.error("offline exception:{}", e.getMessage());
            }
        }
        log.error(cause.getMessage(), cause);
    }
}
