package com.lifekh.gateway.tcp.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.Constants;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.core.domain.OnlineJsonRequest;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.OnlineRequest;
import com.chaos.keep.alive.common.protobuf.Result;
import com.lifekh.gateway.tcp.client.ClientManager;
import com.lifekh.gateway.tcp.manager.TrafficManager;
import com.lifekh.gateway.tcp.route.RouteTransport;
import com.lifekh.gateway.tcp.util.GatewayContext;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.SocketChannel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OnlineServerCommandHandler implements ServerCommandHandler {


    private final ClientManager clientManager;

    private final GatewayContext context;

    private final RouteTransport routeTransport;

    @Autowired
    public final TrafficManager trafficManager;


    @Override
    public void handleCommand(JsonCommand jsonCommand, ChannelHandlerContext ctx) {
        log.info("收到鉴权消息:{}", jsonCommand);
        OnlineJsonRequest request = JSONUtil.toBean(jsonCommand.getBody(), OnlineJsonRequest.class);
        String token = request.getToken();
        OnlineRequest body = OnlineRequest.newBuilder()
                .setToken(token)
                .build();
        Command command = Command.newBuilder()
                .setAppSdkVersion(Constants.APP_SDK_VERSION)
                .setMessageType(MessageType.MESSAGE_TYPE_SEND)
                .setBizType(CommandType.COMMAND_ONLINE)
                .setTimestamp(System.currentTimeMillis())
                .setOperatorNo(jsonCommand.getOperatorNo())
                .setBody(body.toByteString()).build();
        handleCommand(command, ctx);
    }

    @SneakyThrows
    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {

        log.info("客户端上线：{}", command.getOperatorNo() + ":" + command.getDeviceId());
        String operatorNo = command.getOperatorNo();
        String deviceId = command.getDeviceId();



        if (trafficManager.isAuthCountLimit(operatorNo + "-" + deviceId)) {
            Result result = Result.newBuilder().setErrorMessage("鉴权失败次数过多,服务端已拒绝").setSuccess(false).build();
            Command response = Command.newBuilder()
                    .setAppSdkVersion(Constants.APP_SDK_VERSION)
                    .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
                    .setBizType(CommandType.COMMAND_OFFLINE)
                    .setTimestamp(System.currentTimeMillis())
                    .setBody(result.toByteString()).build();
            ctx.writeAndFlush(response);
            log.info("客户端:{}鉴权失败次数过多,服务端已拒绝", command.getOperatorNo() + ":" + command.getDeviceId());
            SocketChannel clientChannel=   (SocketChannel)ctx.channel();
            clientChannel.close();
        }
        SocketChannel existSocket = clientManager.getChannel(command.getOperatorNo(), command.getDeviceId());
        if (existSocket!=null) {
            try {
                clientManager.removeChannel(existSocket);
            }catch (Exception e){
                log.error("gateway-tcp移除socket失败",e);
            }
//            Result result = Result.newBuilder().setErrorMessage("该客户端已经建立连接").setSuccess(false).build();
//            Command response = Command.newBuilder()
//                    .setAppSdkVersion(Constants.APP_SDK_VERSION)
//                    .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
//                    .setBizType(CommandType.COMMAND_OFFLINE)
//                    .setTimestamp(System.currentTimeMillis())
//                    .setBody(result.toByteString()).build();
//            ctx.writeAndFlush(response);
//            log.info("客户端:{}重复连接,服务端已拒绝", command.getOperatorNo() + ":" + command.getDeviceId());

        }
        ChannelFuture future = routeTransport.send(command, command.getOperatorNo());
        future.addListener((ChannelFutureListener) channelFuture -> {
            clientManager.addChannel(command.getOperatorNo(), command.getDeviceId(), (SocketChannel) ctx.channel());
            context.online();
        });

    }


    @Override
    public int getType() {
        return CommandType.COMMAND_ONLINE;
    }
}
