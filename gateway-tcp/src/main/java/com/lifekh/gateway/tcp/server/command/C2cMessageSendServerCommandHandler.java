package com.lifekh.gateway.tcp.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.im.constant.CommandType;
import com.chaos.keep.alive.common.im.constant.MessageType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.im.domain.MessageAckJsonRequest;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest;
import com.chaos.keep.alive.common.protobuf.C2cMessageSendRequest;
import com.chaos.keep.alive.common.protobuf.Command;
import com.lifekh.gateway.tcp.route.RouteTransport;
import com.lifekh.gateway.tcp.route.strategy.ConsistentHashRouteStrategy;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class C2cMessageSendServerCommandHandler implements ServerCommandHandler {

    protected final RouteTransport routeTransport;

    @Override
    public void handleCommand(JsonCommand jsonCommand, ChannelHandlerContext ctx) {
        if(jsonCommand.getMessageType()== MessageType.MESSAGE_TYPE_CLIENT_ACK){
            log.info("收到单聊ack消息,jsonCommand:{}", JSONUtil.toJsonStr(jsonCommand));

            MessageAckJsonRequest request = JSONUtil.toBean(jsonCommand.getBody(), MessageAckJsonRequest.class);
            C2cMessageAckRequest body = C2cMessageAckRequest.newBuilder()
                    .setChatType(request.getChatType())
                    .setChatId(request.getChatId())
                    .setMessageId(request.getMessageId())
                    .setOperatorNo(request.getOperatorNo())
                    .setDeviceId(request.getDeviceId())
                    .setSequence(request.getSequence())
                    .build();


            Command command = Command.newBuilder()
                    .setBizType(jsonCommand.getBizType())
                    .setOperatorNo(jsonCommand.getOperatorNo())
                    .setDeviceId(jsonCommand.getDeviceId())
                    .setMessageType(jsonCommand.getMessageType())
                    .setBody(body.toByteString()).build();
            handleCommand(command, ctx);

        }else{

            log.info("收到单聊消息,jsonCommand:{}", JSONUtil.toJsonStr(jsonCommand));
            MessageSendJsonRequest request = JSONUtil.toBean(jsonCommand.getBody(), MessageSendJsonRequest.class);
            C2cMessageSendRequest body = C2cMessageSendRequest.newBuilder()
                    .setFromOperatorNo(jsonCommand.getOperatorNo())
                    .setToOperatorNo(request.getToOperatorNo())
                    .setChatId(request.getChatId())
                    .setContent(request.getContent())
                    .setMessageId(request.getMessageId())
                    .setCategory(request.getCategory())
                    .build();
            Command command = Command.newBuilder()
                    .setBizType(jsonCommand.getBizType())
                    .setOperatorNo(jsonCommand.getOperatorNo())
                    .setDeviceId(jsonCommand.getDeviceId())
                    .setBody(body.toByteString()).build();
            handleCommand(command, ctx);
        }




    }

    @SneakyThrows
    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        if(command.getMessageType()== MessageType.MESSAGE_TYPE_CLIENT_ACK){
           C2cMessageAckRequest request = C2cMessageAckRequest.newBuilder(C2cMessageAckRequest.parseFrom(command.getBody())).build();

            log.info("fromOperatorNo:{},发送单聊ACK消息,messageId:{} ,toOperatorNo:{}", request.getOperatorNo(), request.getMessageId(), request.getToOperatorNo());
            Command newCommand = Command.newBuilder(command).setBody(request.toByteString()).build();
            // 群聊消息使用hash一致策略，将相同的群消息发送到相同的路由服务
            routeTransport.send(newCommand, request.getToOperatorNo() + "", ConsistentHashRouteStrategy.class);

        }else {
            C2cMessageSendRequest request = C2cMessageSendRequest.newBuilder(C2cMessageSendRequest.parseFrom(command.getBody()))
                    .build();
            log.info("fromOperatorNo:{},发送单聊消息messageId:{} content:{} toOperatorNo:{}", request.getFromOperatorNo(), request.getMessageId(),
                    request.getContent(), request.getToOperatorNo());
            Command newCommand = Command.newBuilder(command).setBody(request.toByteString()).build();
            // 群聊消息使用hash一致策略，将相同的群消息发送到相同的路由服务
            routeTransport.send(newCommand, request.getToOperatorNo(), ConsistentHashRouteStrategy.class);
        }
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_IM_C2C_MESSAGE_SEND;
    }
}
