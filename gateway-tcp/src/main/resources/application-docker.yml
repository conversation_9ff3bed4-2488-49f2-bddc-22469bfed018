keep-alive:
  heartbeat:
    heartbeatInterval: 2000
    readTimeout: 60000
  metrics:
    authInterval: 60000
    authLimit: 10
  port: ${GATEWAY_TCP_PORT:38888}
  protocol: tcp
  routeServers: ${ROUTE_SERVERS:route:9670}
  serverId: ${GATEWAY_SERVER_ID:gateway-tcp-1}
  zk:
    enable: true
    intervalTime: 1000
    retry: 3
    zkServer: ${ZOOKEEPER_HOST:zookeeper}:${ZOOKEEPER_PORT:2181}

server:
  port: 9094

spring:
  application:
    name: gateway-tcp
  main:
    allow-bean-definition-overriding: true
    banner-mode: 'off'
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms

# 日志配置
logging:
  level:
    com.lifekh.gateway.tcp: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/gateway-tcp.log

# 监控端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
