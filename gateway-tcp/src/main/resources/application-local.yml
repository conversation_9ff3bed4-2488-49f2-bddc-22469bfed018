keep-alive:
    heartbeat:
        heartbeatInterval: 2000
        readTimeout: 60000
    metrics:
        authInterval: 60000
        authLimit: 10
    port: 38888
    protocol: tcp
    routeServers: 'null'
    serverId: gateway-tcp-1
    zk:
        enable: true
        intervalTime: 1000
        retry: 3
        zkServer: 172.16.20.214:2181
logging:
    level:
        root: info
server:
    port: 9094
spring:
    application:
        name: gateway-tcp
    main:
        allow-bean-definition-overriding: true
        banner-mode: 'off'
    redis:
        cluster:
            nodes: drc-redis-0-0.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-0-1.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-1-0.redis-svc-1.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-1-1.redis-svc-1.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-2-0.redis-svc-2.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-2-1.redis-svc-2.lifekh-tool-sit.svc.cluster.local:6379
