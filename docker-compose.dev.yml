version: '3.8'

services:
  # 基础服务保持不变
  mysql:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/mysql:8.0.33
    container_name: mysql-dev
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: wownowim_docker
      MYSQL_USER: wownowim_docker
      MYSQL_PASSWORD: wownowim_docker_2025
    ports:
      - "3306:3306"
    volumes:
      - mysql_data_dev:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --authentication-policy=mysql_native_password
      --bind-address=0.0.0.0
    networks:
      - dev-network
    restart: unless-stopped

  mongodb:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/mongo:4.4.8
    container_name: mongodb-dev
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123456
      MONGO_INITDB_DATABASE: REPORT_BEHAVIOR
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data_dev:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - dev-network
    restart: unless-stopped

  redis:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/redis:alpine-linuxarm64
    container_name: redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - dev-network
    restart: unless-stopped

  zookeeper:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/zookeeper:3.9.3-linuxarm64
    platform: linux/arm64
    container_name: zookeeper-dev
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper_data_dev:/var/lib/zookeeper/data
      - zookeeper_logs_dev:/var/lib/zookeeper/log
    networks:
      - dev-network
    restart: unless-stopped

  kafka:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/wurstmeister/kafka:2.13-2.8.1-linuxarm64
    platform: linux/arm64
    container_name: kafka-dev
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092,PLAINTEXT_HOST://0.0.0.0:29092
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    ports:
      - "29092:29092"
    volumes:
      - kafka_data_dev:/var/lib/kafka/data
    networks:
      - dev-network
    restart: unless-stopped

  # 开发环境应用服务
  c2c-app-dev:
    build:
      context: ./c2c
      dockerfile: Dockerfile.dev
    container_name: c2c-application-dev
    depends_on:
      - mysql
      - mongodb
      - redis
      - kafka
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: wownowim_docker
      MYSQL_USERNAME: wownowim_docker
      MYSQL_PASSWORD: wownowim_docker_2025
      MONGODB_HOST: mongodb
      MONGODB_PORT: 27017
      MONGODB_DATABASE: REPORT_BEHAVIOR
      MONGODB_USERNAME: report_behavior
      MONGODB_PASSWORD: report_behavior_2020
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
    ports:
      - "8094:8094"
      - "20886:20886"
      - "5005:5005"  # 远程调试端口
    volumes:
      - ./c2c/target/classes:/app/target/classes:ro  # 挂载编译后的class文件
      - ./c2c/target/lib:/app/lib:ro  # 挂载依赖库
      - ./c2c/src/main/resources:/app/src/main/resources:ro  # 挂载资源文件
      - ./c2c/dev-entrypoint.sh:/app/dev-entrypoint.sh:ro  # 挂载启动脚本
      - c2c_logs_dev:/app/logs
    networks:
      - dev-network
    restart: unless-stopped

  c2g-app-dev:
    build:
      context: ./c2g
      dockerfile: Dockerfile.dev
    container_name: c2g-application-dev
    depends_on:
      - mysql
      - mongodb
      - redis
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: wownowim_docker
      MYSQL_USERNAME: wownowim_docker
      MYSQL_PASSWORD: wownowim_docker_2025
      MONGODB_HOST: mongodb
      MONGODB_PORT: 27017
      MONGODB_DATABASE: REPORT_BEHAVIOR
      MONGODB_USERNAME: report_behavior
      MONGODB_PASSWORD: report_behavior_2020
      REDIS_HOST: redis
      REDIS_PORT: 6379
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
    ports:
      - "8096:8096"
      - "20883:20883"
      - "5006:5006"  # 远程调试端口
    volumes:
      - ./c2g/target/classes:/app/target/classes:ro
      - ./c2g/target/lib:/app/lib:ro
      - ./c2g/src/main/resources:/app/src/main/resources:ro
      - c2g_logs_dev:/app/logs
    networks:
      - dev-network
    restart: unless-stopped

  chat-app-dev:
    build:
      context: ./chat
      dockerfile: Dockerfile.dev
    container_name: chat-application-dev
    depends_on:
      - redis
      - kafka
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
    ports:
      - "8095:8095"
      - "20882:20882"
      - "5007:5007"  # 远程调试端口
    volumes:
      - ./chat/target/classes:/app/target/classes:ro
      - ./chat/target/lib:/app/lib:ro
      - ./chat/src/main/resources:/app/src/main/resources:ro
      - chat_logs_dev:/app/logs
    networks:
      - dev-network
    restart: unless-stopped

  gateway-tcp-app-dev:
    build:
      context: ./gateway-tcp
      dockerfile: Dockerfile.dev
    container_name: gateway-tcp-application-dev
    depends_on:
      - redis
      - zookeeper
      - route-app-dev
    environment:
      SPRING_PROFILES_ACTIVE: docker
      REDIS_HOST: redis
      REDIS_PORT: 6379
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
      GATEWAY_TCP_PORT: 38888
      GATEWAY_SERVER_ID: gateway-tcp-1
      ROUTE_SERVERS: route:9670
    ports:
      - "9094:9094"
      - "38888:38888"
      - "5008:5008"  # 远程调试端口
    volumes:
      - ./gateway-tcp/target/classes:/app/target/classes:ro
      - ./gateway-tcp/target/lib:/app/lib:ro
      - ./gateway-tcp/src/main/resources:/app/src/main/resources:ro
      - gateway_tcp_logs_dev:/app/logs
    networks:
      - dev-network
    restart: unless-stopped

  route-app-dev:
    build:
      context: ./route
      dockerfile: Dockerfile.dev
    container_name: route-application-dev
    depends_on:
      - redis
      - kafka
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
      ROUTE_TCP_PORT: 9670
      ROUTE_SERVER_ID: keep-alive-route
    ports:
      - "9097:9097"
      - "9670:9670"
      - "20990:20990"
      - "5009:5009"  # 远程调试端口
    volumes:
      - ./route/target/classes:/app/target/classes:ro
      - ./route/target/lib:/app/lib:ro
      - ./route/src/main/resources:/app/src/main/resources:ro
      - route_logs_dev:/app/logs
    networks:
      - dev-network
    restart: unless-stopped

  id-server-app-dev:
    build:
      context: ./id-server
      dockerfile: Dockerfile.dev
    container_name: id-server-application-dev
    depends_on:
      - mysql
      - zookeeper
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: wownowim_docker
      MYSQL_USERNAME: wownowim_docker
      MYSQL_PASSWORD: wownowim_docker_2025
      ZOOKEEPER_HOST: zookeeper
      ZOOKEEPER_PORT: 2181
    ports:
      - "8100:8100"
      - "20887:20887"
      - "5010:5010"  # 远程调试端口
    volumes:
      - ./id-server/target/classes:/app/target/classes:ro
      - ./id-server/target/lib:/app/lib:ro
      - ./id-server/src/main/resources:/app/src/main/resources:ro
      - id_server_logs_dev:/app/logs
    networks:
      - dev-network
    restart: unless-stopped

volumes:
  mysql_data_dev:
  mongodb_data_dev:
  redis_data_dev:
  zookeeper_data_dev:
  zookeeper_logs_dev:
  kafka_data_dev:
  c2c_logs_dev:
  c2g_logs_dev:
  chat_logs_dev:
  gateway_tcp_logs_dev:
  route_logs_dev:
  id_server_logs_dev:

networks:
  dev-network:
    driver: bridge
