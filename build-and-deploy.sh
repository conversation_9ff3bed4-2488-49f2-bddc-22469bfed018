#!/bin/bash

# C2C模块Docker构建和部署脚本
# 使用方法: ./build-and-deploy.sh [build|start|stop|restart|logs|clean]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker和Docker Compose是否安装
check_prerequisites() {
    log_step "检查前置条件..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 构建所有模块
build_all_modules() {
    log_step "构建所有模块..."

    # 检查Maven是否安装
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi

    # 构建整个项目
    log_info "开始Maven构建..."
    mvn clean package -DskipTests

    if [ $? -eq 0 ]; then
        log_info "所有模块构建成功"
    else
        log_error "模块构建失败"
        exit 1
    fi
}

# 构建单个模块
build_module() {
    local module=$1
    log_step "构建${module}模块..."

    # 检查Maven是否安装
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi

    # 构建指定模块及其依赖
    log_info "开始Maven构建${module}模块..."
    mvn clean package -DskipTests -pl ${module} -am

    if [ $? -eq 0 ]; then
        log_info "${module}模块构建成功"
    else
        log_error "${module}模块构建失败"
        exit 1
    fi
}

# 创建必要的目录和文件
setup_directories() {
    log_step "创建必要的目录..."
    
    # 创建Docker相关目录
    mkdir -p docker/mysql/init
    mkdir -p docker/mongodb/init
    
    # 创建MySQL初始化脚本（如果不存在）
    if [ ! -f "docker/mysql/init/init.sql" ]; then
        cat > docker/mysql/init/init.sql << 'EOF'
-- C2C模块数据库初始化脚本
-- 这里可以添加必要的表结构和初始数据

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS wownowim_docker CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE wownowim_docker;

-- 示例：创建用户表（根据实际需要修改）
-- CREATE TABLE IF NOT EXISTS users (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY,
--     username VARCHAR(50) NOT NULL UNIQUE,
--     email VARCHAR(100),
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
-- );

-- 插入初始数据（如果需要）
-- INSERT INTO users (username, email) VALUES ('admin', '<EMAIL>');

EOF
        log_info "创建了MySQL初始化脚本"
    fi
    
    # 创建MongoDB初始化脚本（如果不存在）
    if [ ! -f "docker/mongodb/init/init.js" ]; then
        cat > docker/mongodb/init/init.js << 'EOF'
// MongoDB初始化脚本
// 创建数据库和用户

db = db.getSiblingDB('REPORT_BEHAVIOR');

// 创建用户
db.createUser({
    user: 'report_behavior',
    pwd: 'report_behavior_2020',
    roles: [
        {
            role: 'readWrite',
            db: 'REPORT_BEHAVIOR'
        }
    ]
});

// 创建集合（如果需要）
// db.createCollection('messages');

EOF
        log_info "创建了MongoDB初始化脚本"
    fi
}

# 启动服务
start_services() {
    log_step "启动Docker服务..."
    
    setup_directories
    
    # 启动所有服务
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_info "服务启动成功"
        log_info "C2C应用访问地址: http://localhost:8094/c2c"
        log_info "健康检查地址: http://localhost:8094/c2c/actuator/health"
        
        # 等待服务启动
        log_info "等待服务启动完成..."
        sleep 30
        
        # 检查服务状态
        docker-compose ps
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_step "停止Docker服务..."
    docker-compose down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_step "重启Docker服务..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    log_step "显示服务日志..."
    if [ -n "$2" ]; then
        docker-compose logs -f "$2"
    else
        docker-compose logs -f c2c-app
    fi
}

# 清理资源
clean_resources() {
    log_step "清理Docker资源..."
    
    read -p "确定要清理所有Docker资源吗？这将删除所有容器、镜像和数据卷 (y/N): " confirm
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        docker-compose down -v --rmi all
        docker system prune -f
        log_info "资源清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "C2C模块Docker部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 build          - 构建C2C模块"
    echo "  $0 start          - 启动所有服务"
    echo "  $0 stop           - 停止所有服务"
    echo "  $0 restart        - 重启所有服务"
    echo "  $0 logs [service] - 查看日志（默认查看c2c-app日志）"
    echo "  $0 clean          - 清理所有Docker资源"
    echo "  $0 deploy         - 完整部署（构建+启动）"
    echo ""
    echo "示例:"
    echo "  $0 deploy         - 完整部署"
    echo "  $0 logs mysql     - 查看MySQL日志"
    echo "  $0 logs           - 查看C2C应用日志"
}

# 完整部署
full_deploy() {
    log_step "开始完整部署..."
    check_prerequisites
    build_c2c
    start_services
    log_info "部署完成！"
}

# 主函数
main() {
    case "${1:-help}" in
        "build")
            check_prerequisites
            build_c2c
            ;;
        "start")
            check_prerequisites
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs "$@"
            ;;
        "clean")
            clean_resources
            ;;
        "deploy")
            full_deploy
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
