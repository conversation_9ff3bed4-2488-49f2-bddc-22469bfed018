package com.chaos.keep.alive.client.ws;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chaos.keep.alive.client.config.PropertiesUtils;
import com.chaos.keep.alive.client.constant.ClientConstants;
import com.chaos.keep.alive.client.http.IpListRemote;
import com.chaos.keep.alive.client.listener.CommandListener;
import com.chaos.keep.alive.client.IClient;
import com.chaos.keep.alive.client.tcp.CommandHandler;
import com.chaos.keep.alive.client.util.IdUtils;
import com.chaos.keep.alive.client.util.TokenHolder;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.Constants;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.core.domain.JsonResult;
import com.chaos.keep.alive.common.core.domain.address.WsAddressInstance;
import com.chaos.keep.alive.common.core.exception.SystemException;
import com.chaos.keep.alive.common.core.util.AddressUtils;
import com.chaos.keep.alive.common.im.domain.*;
import com.chaos.keep.alive.common.protobuf.*;
import io.netty.util.concurrent.ScheduledFuture;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.*;
import java.io.IOException;
import java.net.URI;
import java.util.Objects;
import java.util.Timer;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.chaos.keep.alive.client.util.HttpUtil.get;


@ClientEndpoint
@Slf4j
public class WebsocketClient implements IClient {


    private final IpListRemote ipListRemote = new IpListRemote(this);

    private final int heartbeatInterval = PropertiesUtils.getHeartbeatInterval();

    @Setter
    private Session session;

    private CommandListener commandListener;

    AtomicBoolean isConnected = new AtomicBoolean(false);

    private final int CONNECT_MAX_RETRY = PropertiesUtils.getConnectMaxRetry() == -1 ? Integer.MAX_VALUE : PropertiesUtils.getConnectMaxRetry();


    @Getter
    private WsAddressInstance addressInstance;


    public WebsocketClient() {
    }


    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
    }

    @OnMessage
    public void onMessage(String message) {

        JsonCommand jsonCommand = JSON.parseObject(message, JsonCommand.class);

        if(jsonCommand.getBizType()==CommandType.COMMAND_ONLINE){
            log.info("收到上线消息:{}",message);
            JsonResult<?> result = JSON.parseObject(jsonCommand.getBody(), JsonResult.class);
            if (result.getSuccess()) {
                log.info("鉴权成功,客户端[{}:{}]在网关[{}]上线成功...", jsonCommand.getOperatorNo(), jsonCommand.getDeviceId(), getAddressInstance().getServerId());
                // 开启心跳调度
                Timer timer = new Timer();
                WsHeartbeatTimerTask wsHeartbeatTimerTask = new WsHeartbeatTimerTask(this);
                timer.scheduleAtFixedRate(wsHeartbeatTimerTask, 0, heartbeatInterval);
                this.isConnected.compareAndSet(false,true);
            }else{
                //鉴权不成功应该要下线
                log.info("鉴权不成功");
            }
            return;
        }
        handleServerCommand(jsonCommand);

    }

    @OnClose
    public void onClose(CloseReason reason) {
        log.info("Disconnected: " + reason.getReasonPhrase());
    }

    @OnError
    public void onError(Throwable throwable) {
        throwable.printStackTrace();
    }

    public void sendMessageText(String message) throws Exception {
        log.info("发送消息:{}", message);
        session.getBasicRemote().sendText(message);
    }

    public void sendMessage(JsonCommand command) throws Exception {
        session.getBasicRemote().sendText(JSONUtil.toJsonStr(command));
    }

    @SneakyThrows
    @Override
    public void fetchC2cOfflineMessage(Long chatId, Long size, Long current, String operatorNo, String deviceId) {

        doFetchOfflineMessage(chatId, 1, size, current, operatorNo, deviceId);

    }

    @SneakyThrows
    @Override
    public void fetchC2gOfflineMessage(Long chatId, Long size, Long current, String operatorNo, String deviceId) {


        doFetchOfflineMessage(chatId, 2, size, current, operatorNo, deviceId);

    }

    @SneakyThrows
    private void doFetchOfflineMessage(Long chatId, Integer chatType, Long size, Long current, String operatorNo, String deviceId) {

        String lastAckUrl;
        String fetchOfflineMessageUrl;
        if (Objects.equals(chatType, com.chaos.keep.alive.common.im.constant.Constants.CHAT_TYPE_C2C)) {
            lastAckUrl = PropertiesUtils.getFetchC2cLastAckUrl();

            fetchOfflineMessageUrl = PropertiesUtils.getFetchC2cOfflineMessageUrl();
        } else {
            lastAckUrl = PropertiesUtils.getFetchC2gLastAckUrl();
            fetchOfflineMessageUrl = PropertiesUtils.getFetchC2gOfflineMessageUrl();
        }


        java.util.Map<String, String> queryParams = new java.util.HashMap<>();
        queryParams.put("chatId", chatId + "");
        queryParams.put("size", size + "");
        queryParams.put("current", current + "");
        queryParams.put("operatorNo", operatorNo);
        queryParams.put("deviceId", deviceId);


        String lastAckResponse = get(lastAckUrl, queryParams, null);

        JsonResult<Long> lastjsonResult = JSON.parseObject(lastAckResponse, new com.alibaba.fastjson.TypeReference<JsonResult<Long>>() {
        });

        // 获取 data 字段
        Long lastdata = lastjsonResult.getData();

        queryParams.put("startMessageId", lastdata + "");

        String response = get(fetchOfflineMessageUrl, queryParams, null);


        // 解析整个响应为 JsonResult
        JsonResult<com.alibaba.fastjson.JSONObject> jsonResult = JSON.parseObject(response, new com.alibaba.fastjson.TypeReference<JsonResult<com.alibaba.fastjson.JSONObject>>() {
        });

        // 获取 data 字段
        com.alibaba.fastjson.JSONObject data = jsonResult.getData();
        if (data == null) {
            log.info("{} 拉取到离线消息为空", TokenHolder.getTokenInfo(this).getOperatorNo());
            return;
        }
        // 解析 data 字段为 BasePage
        BasePage<MessageJsonPush> page = data.toJavaObject(new com.alibaba.fastjson.TypeReference<BasePage<MessageJsonPush>>() {
        });

        log.info("拉取到离线消息总数:{}  , size:{}, current:{} ,chatId:{},chatType:{}", page.getPagination().getTotal(), size, page.getPagination().getCurrent(), chatId, chatType);

        for (MessageJsonPush messageJsonPush : page.getList()) {
            log.info("拉取到一条离线消息 content:{} fromOperatorNo:{} chatId:{} chatType:{} messageId:{}", messageJsonPush.getContent(), messageJsonPush.getFromOperatorNo(),
                    messageJsonPush.getChatId(), messageJsonPush.getChatType(), messageJsonPush.getMessageId());
            if (Objects.equals(chatType, com.chaos.keep.alive.common.im.constant.Constants.CHAT_TYPE_C2C)) {
                sendC2cAck(messageJsonPush.getChatId(), messageJsonPush.getToOperatorNo(), messageJsonPush.getMessageId(), messageJsonPush.getChatType());
            } else {
                sendC2gAck(messageJsonPush.getChatId(), messageJsonPush.getMessageId(), messageJsonPush.getChatType());
            }

        }

        if (page.getPagination().getTotal() > page.getPagination().getPageSize() * page.getPagination().getCurrent()) {
            fetchC2gOfflineMessage(chatId, size, page.getPagination().getCurrent() + 1, operatorNo, deviceId);
        }

    }

    public void sendMessage(Command command) {
        try {
            session.getBasicRemote().sendText(JSONUtil.toJsonStr(JsonCommand.convert(command)));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void connect(String token, String operatorNo, String deviceId, String appId, Integer
            appNo, CommandListener commandListener) {
        this.commandListener = commandListener;
        TokenHolder.TokenInfo tokenInfo = new TokenHolder.TokenInfo(token, operatorNo, deviceId, appId);
        TokenHolder.setTokenInfo(this, tokenInfo);
        reconnect();

    }

    @Override
    public void sendC2cAck(Long chatId, String toOperatorNo, Long messageId, Integer chatType) {


        MessageAckJsonRequest jsonRequest = new MessageAckJsonRequest();
        jsonRequest.setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo());
        jsonRequest.setMessageId(messageId);
        jsonRequest.setChatType(chatType);
        jsonRequest.setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId());
        jsonRequest.setChatId(chatId);


        JsonCommand jsonCommand = new JsonCommand();
        jsonCommand.setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo());
        jsonCommand.setAppId(TokenHolder.getTokenInfo(this).getAppId());
        jsonCommand.setAppSdkVersion(Constants.APP_SDK_VERSION);
        jsonCommand.setBizType(com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2C_MESSAGE_SEND);
        jsonCommand.setTimestamp(System.currentTimeMillis());
        jsonCommand.setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId());
        jsonCommand.setMessageType(com.chaos.keep.alive.common.im.constant.MessageType.MESSAGE_TYPE_CLIENT_ACK);
        jsonCommand.setBody(JSONUtil.toJsonStr(jsonRequest));

        log.info("{}发送单聊ACK,toOperatorNo:{},messageId:{},chatType:{}", jsonCommand.getOperatorNo(), toOperatorNo, messageId, chatType);
        try {
            sendMessage(jsonCommand);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void sendC2gAck(Long chatId, Long messageId, Integer chatType) {
        MessageAckJsonRequest jsonRequest = new MessageAckJsonRequest();
        jsonRequest.setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo());
        jsonRequest.setMessageId(messageId);
        jsonRequest.setChatType(chatType);
        jsonRequest.setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId());
        jsonRequest.setChatId(chatId);

        JsonCommand jsonCommand = new JsonCommand();
        jsonCommand.setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo());
        jsonCommand.setAppId(TokenHolder.getTokenInfo(this).getAppId());
        jsonCommand.setAppSdkVersion(Constants.APP_SDK_VERSION);
        jsonCommand.setBizType(com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2G_MESSAGE_SEND);
        jsonCommand.setTimestamp(System.currentTimeMillis());
        jsonCommand.setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId());
        jsonCommand.setMessageType(com.chaos.keep.alive.common.im.constant.MessageType.MESSAGE_TYPE_CLIENT_ACK);
        jsonCommand.setBody(JSONUtil.toJsonStr(jsonRequest));

        log.info("{}发送群聊ACK,chatId:{},messageId:{},chatType:{}", jsonCommand.getOperatorNo(), chatId, messageId, chatType);
        try {
            sendMessage(jsonCommand);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void sendText(Long chatId, String toOperatorNo, String content, Integer category) {

    }

    @Override
    public void sendC2cMessage(Long chatId, String toOperatorNo, String content, Integer category) {

        MessageSendJsonRequest requestJson = new MessageSendJsonRequest();
        requestJson.setFromOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo());
        requestJson.setToOperatorNo(toOperatorNo);
        requestJson.setContent(content);
        requestJson.setCategory(category);
        requestJson.setChatId(chatId);
        requestJson.setMessageId(IdUtils.getId("message"));

        JsonCommand jsonCommand = new JsonCommand();

        jsonCommand.setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo());
        jsonCommand.setMessageType(MessageType.MESSAGE_TYPE_SEND);
        jsonCommand.setBizType(com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2C_MESSAGE_SEND);
        jsonCommand.setTimestamp(System.currentTimeMillis());
        jsonCommand.setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId());
        jsonCommand.setBody(JSONUtil.toJsonStr(requestJson));


        log.info("{}发送单聊消息给operatorNo:{},content:{},category:{}", jsonCommand.getOperatorNo(), toOperatorNo, content, category);
        try {
            sendMessage(jsonCommand);
        } catch (Exception e) {
            log.error("发送群聊消息失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void sendC2gMessage(Long chatId, String content, Integer category) {

        MessageSendJsonRequest requestJson = new MessageSendJsonRequest();
        requestJson.setMessageId(IdUtils.getId("message"));
        requestJson.setChatType(2);
        requestJson.setChatId(chatId);
        requestJson.setCategory(category);
        requestJson.setContent(content);
        requestJson.setFromOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo());

        JsonCommand jsonCommand = new JsonCommand();
        jsonCommand.setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo());
        jsonCommand.setMessageType(com.chaos.keep.alive.common.im.constant.MessageType.MESSAGE_TYPE_SEND);
        jsonCommand.setBizType(com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2G_MESSAGE_SEND);
        jsonCommand.setTimestamp(System.currentTimeMillis());
        jsonCommand.setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId());
        jsonCommand.setBody(JSONUtil.toJsonStr(requestJson));

        log.info("{}发送ws群聊消息到chatId:{},content:{},category:{}", jsonCommand.getOperatorNo(), chatId, content, category);
        try {
            sendMessage(jsonCommand);
        } catch (Exception e) {
            log.error("发送群聊消息失败", e);
            throw new RuntimeException(e);
        }
    }

    public boolean isConnected() {
        return isConnected.get();
    }

    @Override
    public void reportRiderLocation(long riderId, String appId, Long timestamp, Double latitude, Double longitude) {

    }

    @Override
    public void reportLocation(long driverId, String appId, Long timestamp, Double latitude, Double longitude, Double speed, Integer direction, String orderId) {

    }

    @SneakyThrows
    void reconnect() {

        int retry = 0;
        while (retry < CONNECT_MAX_RETRY) {
            try {

                if (StrUtil.isNotBlank(PropertiesUtils.getKaGatewayWsAddress())) {
                    addressInstance = AddressUtils.parseWsAddress(PropertiesUtils.getKaGatewayWsAddress());
                } else {
                    //调用iplist服务获取gateway的ip进行连接
                    JsonResult<?> jsonResult = ipListRemote.get();
                    if (jsonResult.getSuccess()) {
                        addressInstance = JSONUtil.toBean((JSONObject) jsonResult.getData(), WsAddressInstance.class);
                    } else throw new SystemException(jsonResult.getErrorMessage());
                }
                // 连接网关服务
                this.setSession(ContainerProvider.getWebSocketContainer().connectToServer(this, new URI(addressInstance.getWsAddress())));

                isConnected.compareAndSet(false, true);
                log.info("与网关[{}]的连接已建立", addressInstance.getServerId());
                // 注册客户端到网关服务
                online();
                return;

            } catch (Exception e) {
                log.error("网关连接失败重试", e);
                Thread.sleep(3000);
                retry++;
            }
        }
        throw new SystemException("与网关建立连接失败");
    }

    void handleServerCommand(JsonCommand command) {
        commandListener.onCommand(command);
    }

    private void online() throws Exception {
        String token = TokenHolder.getTokenInfo(this).getToken();
        String operatorNo = TokenHolder.getTokenInfo(this).getOperatorNo();
        String deviceId = TokenHolder.getTokenInfo(this).getDeviceId();

        OnlineJsonRequest jsonRequest = new OnlineJsonRequest();
        jsonRequest.setToken(token);

        JsonCommand jsonCommand = new JsonCommand();
        jsonCommand.setAppSdkVersion(Constants.APP_SDK_VERSION);
        jsonCommand.setMessageType(MessageType.MESSAGE_TYPE_SEND);
        jsonCommand.setBizType(CommandType.COMMAND_ONLINE);
        jsonCommand.setTimestamp(System.currentTimeMillis());
        jsonCommand.setOperatorNo(operatorNo);
        jsonCommand.setDeviceId(deviceId);
        jsonCommand.setBody(JSONUtil.toJsonStr(jsonRequest));


        sendMessage(jsonCommand);
        log.debug("[{}:{}]通知网关[{}]上线...", operatorNo, ClientConstants.DEVICE_ID, addressInstance.getServerId());
    }

}
