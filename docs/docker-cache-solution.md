# Docker 镜像缓存解决方案

## 🎯 问题背景

在开发过程中，Docker 镜像缓存可能导致以下问题：
- 配置文件更新后，容器仍使用旧配置
- Dockerfile 修改后，镜像没有重新构建
- JDWP 调试配置更新不生效
- 环境变量变更被忽略

## ✅ 解决方案

### 1. 版本标签机制

每个服务的 Docker Compose 配置都添加了版本标签：

```yaml
c2c-app-dev:
  build:
    context: ./c2c
    dockerfile: Dockerfile.dev
    args:
      - BUILD_DATE=${BUILD_DATE:-$(date +%Y%m%d%H%M%S)}
      - BUILD_VERSION=${BUILD_VERSION:-dev}
  image: keep-alive-con-c2c-app-dev:${BUILD_DATE:-latest}
```

### 2. Dockerfile 构建参数

每个 Dockerfile.dev 都添加了构建参数：

```dockerfile
# 构建参数，用于避免缓存
ARG BUILD_DATE
ARG BUILD_VERSION=dev
LABEL build.date=$BUILD_DATE
LABEL build.version=$BUILD_VERSION
```

### 3. 自动化重建脚本

使用 `scripts/rebuild-service.sh` 脚本进行自动化重建：

```bash
# 重建单个服务
./scripts/rebuild-service.sh c2g

# 重建所有服务
./scripts/rebuild-service.sh all

# 指定版本重建
./scripts/rebuild-service.sh c2g v1.0.1
```

## 🚀 使用方法

### 快速重建单个服务

```bash
# 重建 c2g 服务（推荐）
./scripts/rebuild-service.sh c2g

# 手动重建（如果脚本不可用）
BUILD_DATE=$(date +%Y%m%d%H%M%S) docker-compose -f docker-compose.dev.yml build --no-cache c2g-app-dev
docker-compose -f docker-compose.dev.yml up -d c2g-app-dev
```

### 重建所有服务

```bash
# 使用脚本重建所有服务
./scripts/rebuild-service.sh all

# 手动重建所有服务
BUILD_DATE=$(date +%Y%m%d%H%M%S) docker-compose -f docker-compose.dev.yml build --no-cache
docker-compose -f docker-compose.dev.yml up -d
```

### 验证重建结果

```bash
# 查看镜像标签
docker images | grep "keep-alive-con.*app-dev"

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看服务日志
docker logs c2g-application-dev --tail 20
```

## 🔧 故障排除

### 1. 配置更新不生效

**症状：** 修改了 Dockerfile 或配置文件，但容器仍使用旧配置

**解决方案：**
```bash
# 停止服务
docker-compose -f docker-compose.dev.yml stop c2g-app-dev

# 删除旧镜像
docker rmi keep-alive-con-c2g-app-dev

# 重建并启动
./scripts/rebuild-service.sh c2g
```

### 2. JDWP 调试端口问题

**症状：** JDWP 传输初始化错误

**解决方案：**
1. 确认 Dockerfile.dev 中的 JDWP 地址格式：
   ```dockerfile
   -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=0.0.0.0:5006
   ```
2. 重建镜像：
   ```bash
   ./scripts/rebuild-service.sh c2g
   ```

### 3. 环境变量不更新

**症状：** docker-compose.dev.yml 中的环境变量更新不生效

**解决方案：**
```bash
# 重新创建容器（不重建镜像）
docker-compose -f docker-compose.dev.yml up -d --force-recreate c2g-app-dev

# 或者完全重建
./scripts/rebuild-service.sh c2g
```

## 📊 最佳实践

### 1. 开发流程

1. **修改代码/配置** → 无需重建镜像（热重载）
2. **修改 Dockerfile** → 使用重建脚本
3. **修改环境变量** → 重新创建容器
4. **修改依赖** → 重新编译 + 重建镜像

### 2. 性能优化

- **优先使用热重载**：代码和资源文件变更
- **按需重建**：只重建修改的服务
- **批量重建**：大量配置变更时使用 `all` 选项

### 3. 版本管理

```bash
# 开发版本（默认）
./scripts/rebuild-service.sh c2g

# 测试版本
./scripts/rebuild-service.sh c2g test-v1.0

# 发布版本
./scripts/rebuild-service.sh c2g release-v1.0
```

## 🎉 优势总结

1. **彻底解决缓存问题**：每次重建都使用唯一时间戳
2. **自动化操作**：一键重建，减少手动错误
3. **版本可追溯**：镜像标签包含构建时间
4. **灵活性高**：支持单个/批量重建
5. **开发友好**：保留热重载功能

## 📝 注意事项

1. **重建会中断服务**：建议在开发环境使用
2. **镜像占用空间**：定期清理旧镜像
3. **网络依赖**：重建需要下载基础镜像
4. **构建时间**：首次重建较慢，后续较快

使用这套解决方案，可以彻底避免 Docker 镜像缓存导致的配置不生效问题！
