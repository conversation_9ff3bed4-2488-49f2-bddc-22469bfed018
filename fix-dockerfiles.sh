#!/bin/bash

# 修复所有模块的Dockerfile.dev文件

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 模块列表
MODULES=("chat" "gateway-tcp" "route" "id-server")

log_step "修复所有模块的Dockerfile.dev文件..."

for module in "${MODULES[@]}"; do
    if [ -f "$module/Dockerfile.dev" ]; then
        log_info "修复 $module/Dockerfile.dev"
        
        # 移除COPY target/lib/*.jar这一行
        sed -i.bak '/COPY target\/lib\/\*\.jar \/app\/lib\//d' "$module/Dockerfile.dev"
        
        # 删除备份文件
        rm -f "$module/Dockerfile.dev.bak"
        
        log_info "✅ $module/Dockerfile.dev 修复完成"
    else
        log_info "❌ $module/Dockerfile.dev 不存在"
    fi
done

log_info "所有Dockerfile.dev文件修复完成"
