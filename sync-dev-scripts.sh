#!/bin/bash

# 同步开发脚本到各个模块目录

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 模块列表
MODULES=("c2c" "c2g" "chat" "gateway-tcp" "route" "id-server")

# 同步启动脚本
sync_entrypoint() {
    log_step "同步开发环境启动脚本到各个模块..."
    
    if [ ! -f "docker/dev-entrypoint.sh" ]; then
        log_error "找不到源文件: docker/dev-entrypoint.sh"
        exit 1
    fi
    
    for module in "${MODULES[@]}"; do
        if [ -d "$module" ]; then
            log_info "复制启动脚本到 $module 模块"
            cp docker/dev-entrypoint.sh "$module/"
            chmod +x "$module/dev-entrypoint.sh"
        else
            log_warn "模块目录不存在: $module"
        fi
    done
    
    log_info "启动脚本同步完成"
}

# 清理模块中的启动脚本
clean_entrypoint() {
    log_step "清理各个模块中的启动脚本..."
    
    for module in "${MODULES[@]}"; do
        if [ -f "$module/dev-entrypoint.sh" ]; then
            log_info "删除 $module 模块中的启动脚本"
            rm "$module/dev-entrypoint.sh"
        fi
    done
    
    log_info "启动脚本清理完成"
}

# 检查文件状态
check_status() {
    log_step "检查启动脚本状态..."
    
    echo "源文件:"
    if [ -f "docker/dev-entrypoint.sh" ]; then
        echo "  ✅ docker/dev-entrypoint.sh"
    else
        echo "  ❌ docker/dev-entrypoint.sh (不存在)"
    fi
    
    echo ""
    echo "模块文件:"
    for module in "${MODULES[@]}"; do
        if [ -f "$module/dev-entrypoint.sh" ]; then
            echo "  ✅ $module/dev-entrypoint.sh"
        else
            echo "  ❌ $module/dev-entrypoint.sh (不存在)"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo "开发脚本同步工具"
    echo ""
    echo "使用方法:"
    echo "  $0 sync    - 同步启动脚本到各个模块"
    echo "  $0 clean   - 清理各个模块中的启动脚本"
    echo "  $0 status  - 检查启动脚本状态"
    echo ""
    echo "示例:"
    echo "  $0 sync    - 将 docker/dev-entrypoint.sh 复制到各个模块"
    echo "  $0 status  - 查看哪些模块有启动脚本"
}

# 主函数
main() {
    case "${1:-help}" in
        "sync")
            sync_entrypoint
            ;;
        "clean")
            clean_entrypoint
            ;;
        "status")
            check_status
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
