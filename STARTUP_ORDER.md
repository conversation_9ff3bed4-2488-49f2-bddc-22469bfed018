# 服务启动顺序说明

本文档说明各个服务的启动顺序和依赖关系。

## 服务依赖关系图

```
基础服务层:
├── MySQL (数据库)
├── MongoDB (文档数据库)
├── Redis (缓存)
├── Zookeeper (注册中心)
└── Kafka (消息队列)

核心服务层:
├── ID-Server (依赖: MySQL, Zookeeper)
└── Route (依赖: Redis, Kaf<PERSON>, Zookeeper)

业务服务层:
├── C2C (依赖: MySQL, MongoDB, Redis, Kafka, Zookeeper)
├── C2G (依赖: MySQL, MongoDB, Redis, Zookeeper)
└── Chat (依赖: <PERSON><PERSON><PERSON>, Zookeeper)

网关服务层:
└── Gateway-TCP (依赖: <PERSON><PERSON>, Zookeeper, Route)
```

## 启动顺序

### 第一阶段：基础服务
1. **Zookeeper** - 注册中心，其他服务的依赖
2. **MySQL** - 关系型数据库
3. **MongoDB** - 文档数据库
4. **Redis** - 缓存服务
5. **Kafka** - 消息队列

### 第二阶段：核心服务
6. **ID-Server** - ID生成服务，为其他服务提供唯一ID
7. **Route** - 路由服务，为Gateway提供路由功能

### 第三阶段：业务服务
8. **C2C** - 点对点聊天服务
9. **C2G** - 群组聊天服务
10. **Chat** - 聊天统一服务

### 第四阶段：网关服务
11. **Gateway-TCP** - TCP网关，处理客户端连接

## 服务端口分配

### HTTP端口
- ID-Server: 8100
- C2C: 8094
- Chat: 8095
- C2G: 8096
- Route: 9097
- Gateway-TCP: 9094

### Dubbo端口
- ID-Server: 20887
- C2C: 20886
- Chat: 20882
- C2G: 20883
- Route: 20990

### TCP端口
- Gateway-TCP: 38888 (客户端连接)
- Route: 9670 (内部通信)

### 基础服务端口
- MySQL: 3306
- MongoDB: 27017
- Redis: 6379
- Zookeeper: 2181
- Kafka: 29092

## 健康检查策略

所有应用服务都配置了健康检查：
- 检查间隔：30秒
- 超时时间：10秒
- 启动等待：60秒
- 重试次数：3次

## 故障恢复

### 自动重启策略
所有服务都配置了 `restart: unless-stopped` 策略，在以下情况下会自动重启：
- 服务异常退出
- 容器崩溃
- 健康检查失败

### 依赖服务故障
- 如果基础服务（MySQL、Redis等）故障，依赖的业务服务会等待其恢复
- 如果业务服务故障，不会影响其他独立的业务服务
- Gateway-TCP依赖Route服务，Route故障时Gateway会失去路由能力

## 扩展部署

### 水平扩展
可以通过以下方式进行水平扩展：

```bash
# 扩展C2C服务到3个实例
docker-compose up -d --scale c2c-app=3

# 扩展Gateway-TCP到2个实例
docker-compose up -d --scale gateway-tcp-app=2
```

### 负载均衡
- Dubbo服务通过Zookeeper自动实现负载均衡
- HTTP服务可以通过Nginx等反向代理实现负载均衡
- TCP服务可以通过Route服务实现负载均衡

## 监控建议

### 关键指标监控
1. **服务可用性**: 通过健康检查端点监控
2. **响应时间**: 监控各服务的响应时间
3. **错误率**: 监控服务错误日志
4. **资源使用**: 监控CPU、内存、磁盘使用情况

### 日志收集
所有服务的日志都存储在对应的Docker卷中：
- c2c_logs: C2C服务日志
- c2g_logs: C2G服务日志
- chat_logs: Chat服务日志
- gateway_tcp_logs: Gateway-TCP服务日志
- route_logs: Route服务日志
- id_server_logs: ID-Server服务日志

### 告警配置
建议配置以下告警：
- 服务不可用告警
- 响应时间过长告警
- 错误率过高告警
- 资源使用率过高告警
- 磁盘空间不足告警
