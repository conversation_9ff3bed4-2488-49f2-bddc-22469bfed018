#!/bin/bash

# 测试依赖复制优化效果

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 测试模块
TEST_MODULE="c2c"

# 清理测试环境
cleanup_test_env() {
    log_step "清理测试环境..."
    
    # 清理模块缓存
    rm -f "${TEST_MODULE}/.dep-cache"
    rm -rf "${TEST_MODULE}/target/lib"
    
    # 清理全局缓存
    rm -rf ".dev-cache"
    
    log_info "测试环境清理完成"
}

# 测试首次编译
test_first_compile() {
    log_step "测试首次编译（包含依赖复制）..."
    
    cleanup_test_env
    
    local start_time=$(date +%s)
    ./dev-hot-reload.sh compile "$TEST_MODULE" > /dev/null 2>&1
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "  首次编译耗时: ${duration}秒"
    
    # 检查结果
    if [ -d "${TEST_MODULE}/target/lib" ] && [ -f "${TEST_MODULE}/.dep-cache" ]; then
        log_info "✅ 首次编译成功，缓存已创建"
    else
        log_error "❌ 首次编译失败"
        return 1
    fi
    
    return $duration
}

# 测试增量编译
test_incremental_compile() {
    log_step "测试增量编译（跳过依赖复制）..."
    
    local start_time=$(date +%s)
    ./dev-hot-reload.sh compile "$TEST_MODULE" > /dev/null 2>&1
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "  增量编译耗时: ${duration}秒"
    
    return $duration
}

# 测试pom.xml修改后的编译
test_pom_modified_compile() {
    log_step "测试pom.xml修改后的编译..."
    
    # 模拟pom.xml修改
    touch "${TEST_MODULE}/pom.xml"
    
    local start_time=$(date +%s)
    ./dev-hot-reload.sh compile "$TEST_MODULE" > /dev/null 2>&1
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "  pom修改后编译耗时: ${duration}秒"
    
    return $duration
}

# 测试快速重载
test_fast_reload() {
    log_step "测试快速重载（仅Java文件）..."
    
    local start_time=$(date +%s)
    ./dev-hot-reload.sh fast "$TEST_MODULE" > /dev/null 2>&1
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "  快速重载耗时: ${duration}秒"
    
    return $duration
}

# 测试全局缓存效果
test_global_cache() {
    log_step "测试全局缓存效果..."
    
    # 清理模块缓存，保留全局缓存
    rm -f "${TEST_MODULE}/.dep-cache"
    rm -rf "${TEST_MODULE}/target/lib"
    
    local start_time=$(date +%s)
    ./dev-hot-reload.sh compile "$TEST_MODULE" > /dev/null 2>&1
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo "  全局缓存复制耗时: ${duration}秒"
    
    return $duration
}

# 运行完整性能测试
run_performance_test() {
    log_step "运行完整性能测试..."
    
    echo ""
    echo "🧪 性能测试报告:"
    echo "=================="
    
    # 测试1: 首次编译
    test_first_compile
    local first_time=$?
    
    # 测试2: 增量编译
    test_incremental_compile
    local incremental_time=$?
    
    # 测试3: pom修改后编译
    test_pom_modified_compile
    local pom_modified_time=$?
    
    # 测试4: 快速重载
    test_fast_reload
    local fast_time=$?
    
    # 测试5: 全局缓存
    test_global_cache
    local cache_time=$?
    
    echo ""
    echo "📊 性能对比分析:"
    echo "=================="
    printf "%-20s %10s %15s\n" "测试场景" "耗时(秒)" "相对首次编译"
    echo "----------------------------------------"
    printf "%-20s %10d %15s\n" "首次编译" "$first_time" "100%"
    
    if [ $first_time -gt 0 ]; then
        local incremental_percent=$(( incremental_time * 100 / first_time ))
        local pom_percent=$(( pom_modified_time * 100 / first_time ))
        local fast_percent=$(( fast_time * 100 / first_time ))
        local cache_percent=$(( cache_time * 100 / first_time ))
        
        printf "%-20s %10d %15s\n" "增量编译" "$incremental_time" "${incremental_percent}%"
        printf "%-20s %10d %15s\n" "pom修改后" "$pom_modified_time" "${pom_percent}%"
        printf "%-20s %10d %15s\n" "快速重载" "$fast_time" "${fast_percent}%"
        printf "%-20s %10d %15s\n" "全局缓存" "$cache_time" "${cache_percent}%"
    fi
    
    echo ""
    echo "🎯 优化效果评估:"
    echo "=================="
    
    if [ $incremental_time -lt $(( first_time / 2 )) ]; then
        log_info "✅ 增量编译优化效果: 优秀 (节省超过50%时间)"
    elif [ $incremental_time -lt $(( first_time * 3 / 4 )) ]; then
        log_info "👍 增量编译优化效果: 良好 (节省25-50%时间)"
    else
        log_warn "⚠️ 增量编译优化效果: 一般 (节省少于25%时间)"
    fi
    
    if [ $fast_time -lt $(( first_time / 3 )) ]; then
        log_info "✅ 快速重载优化效果: 优秀 (节省超过66%时间)"
    elif [ $fast_time -lt $(( first_time / 2 )) ]; then
        log_info "👍 快速重载优化效果: 良好 (节省33-66%时间)"
    else
        log_warn "⚠️ 快速重载优化效果: 一般 (节省少于33%时间)"
    fi
}

# 测试跳过逻辑
test_skip_logic() {
    log_step "测试跳过逻辑..."
    
    # 确保有缓存
    ./dev-hot-reload.sh compile "$TEST_MODULE" > /dev/null 2>&1
    
    echo ""
    echo "🔍 跳过逻辑测试:"
    echo "=================="
    
    # 测试1: 正常情况（应该跳过）
    echo "测试1: 依赖无变化（应该跳过复制）"
    local output=$(./dev-hot-reload.sh compile "$TEST_MODULE" 2>&1)
    if echo "$output" | grep -q "跳过"; then
        log_info "✅ 正确跳过了依赖复制"
    else
        log_warn "⚠️ 未能跳过依赖复制"
    fi
    
    # 测试2: pom.xml修改（应该复制）
    echo ""
    echo "测试2: pom.xml修改（应该重新复制）"
    touch "${TEST_MODULE}/pom.xml"
    local output=$(./dev-hot-reload.sh compile "$TEST_MODULE" 2>&1)
    if echo "$output" | grep -q "复制.*依赖"; then
        log_info "✅ 正确检测到pom.xml变化并重新复制"
    else
        log_warn "⚠️ 未能检测到pom.xml变化"
    fi
    
    # 测试3: lib目录删除（应该复制）
    echo ""
    echo "测试3: lib目录删除（应该重新复制）"
    rm -rf "${TEST_MODULE}/target/lib"
    local output=$(./dev-hot-reload.sh compile "$TEST_MODULE" 2>&1)
    if echo "$output" | grep -q "复制.*依赖"; then
        log_info "✅ 正确检测到lib目录缺失并重新复制"
    else
        log_warn "⚠️ 未能检测到lib目录缺失"
    fi
}

# 显示帮助信息
show_help() {
    echo "依赖复制优化测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 perf      - 运行完整性能测试"
    echo "  $0 skip      - 测试跳过逻辑"
    echo "  $0 first     - 测试首次编译"
    echo "  $0 inc       - 测试增量编译"
    echo "  $0 fast      - 测试快速重载"
    echo "  $0 cache     - 测试全局缓存"
    echo "  $0 clean     - 清理测试环境"
    echo "  $0 all       - 运行所有测试"
    echo ""
    echo "示例:"
    echo "  $0 all       - 运行完整测试套件"
    echo "  $0 perf      - 只运行性能测试"
}

# 主函数
main() {
    case "${1:-help}" in
        "perf")
            run_performance_test
            ;;
        "skip")
            test_skip_logic
            ;;
        "first")
            test_first_compile
            ;;
        "inc")
            test_incremental_compile
            ;;
        "fast")
            test_fast_reload
            ;;
        "cache")
            test_global_cache
            ;;
        "clean")
            cleanup_test_env
            ;;
        "all")
            run_performance_test
            echo ""
            test_skip_logic
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
