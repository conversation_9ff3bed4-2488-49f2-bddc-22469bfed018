# Keep-Alive项目Docker部署指南

本文档介绍如何将Keep-Alive项目的所有模块部署到Docker环境中，包括C2C、C2G、Chat、Gateway-TCP、Route和ID-Server模块。

## 目录结构

```
keep-alive-con/
├── c2c/
│   ├── Dockerfile                          # C2C模块的Docker镜像构建文件
│   ├── .dockerignore                       # Docker构建忽略文件
│   └── src/main/resources/
│       └── application-docker.yml          # Docker环境配置文件
├── c2g/
│   ├── Dockerfile                          # C2G模块的Docker镜像构建文件
│   ├── .dockerignore                       # Docker构建忽略文件
│   └── src/main/resources/
│       └── application-docker.yml          # Docker环境配置文件
├── chat/
│   ├── Dockerfile                          # Chat模块的Docker镜像构建文件
│   ├── .dockerignore                       # Docker构建忽略文件
│   └── src/main/resources/
│       └── application-docker.yml          # Docker环境配置文件
├── gateway-tcp/
│   ├── Dockerfile                          # Gateway-TCP模块的Docker镜像构建文件
│   ├── .dockerignore                       # Docker构建忽略文件
│   └── src/main/resources/
│       └── application-docker.yml          # Docker环境配置文件
├── route/
│   ├── Dockerfile                          # Route模块的Docker镜像构建文件
│   ├── .dockerignore                       # Docker构建忽略文件
│   └── src/main/resources/
│       └── application-docker.yml          # Docker环境配置文件
├── id-server/
│   ├── Dockerfile                          # ID-Server模块的Docker镜像构建文件
│   ├── .dockerignore                       # Docker构建忽略文件
│   └── src/main/resources/
│       └── application-docker.yml          # Docker环境配置文件
├── docker-compose.yml                      # Docker Compose编排文件
├── build-and-deploy.sh                     # 构建和部署脚本
├── docker/
│   ├── mysql/init/init.sql                 # MySQL初始化脚本
│   └── mongodb/init/init.js                # MongoDB初始化脚本
└── DOCKER_DEPLOYMENT.md                    # 本文档
```

## 前置条件

1. **Docker**: 版本 20.10+
2. **Docker Compose**: 版本 1.29+
3. **Maven**: 版本 3.6+
4. **Java**: 版本 8+

### 安装Docker和Docker Compose

#### macOS
```bash
# 安装Docker Desktop
brew install --cask docker

# 或者下载Docker Desktop安装包
# https://www.docker.com/products/docker-desktop
```

#### Linux (Ubuntu/Debian)
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 快速开始

### 1. 一键部署

```bash
# 给脚本执行权限
chmod +x build-and-deploy.sh

# 完整部署（构建+启动）
./build-and-deploy.sh deploy
```

### 2. 分步部署

```bash
# 1. 构建C2C模块
./build-and-deploy.sh build

# 2. 启动服务
./build-and-deploy.sh start
```

## 服务说明

部署完成后，将启动以下服务：

| 服务名称 | 容器名称 | 端口映射 | 说明 |
|---------|----------|----------|------|
| c2c-app | c2c-application | 8094:8094, 20886:20886 | C2C主应用 |
| c2g-app | c2g-application | 8096:8096, 20883:20883 | C2G主应用 |
| chat-app | chat-application | 8095:8095, 20882:20882 | Chat主应用 |
| gateway-tcp-app | gateway-tcp-application | 9094:9094, 38888:38888 | Gateway-TCP应用 |
| route-app | route-application | 9097:9097, 9670:9670, 20990:20990 | Route路由服务 |
| id-server-app | id-server-application | 8100:8100, 20887:20887 | ID生成服务 |
| mysql | c2c-mysql | 3306:3306 | MySQL数据库 |
| mongodb | c2c-mongodb | 27017:27017 | MongoDB数据库 |
| redis | c2c-redis | 6379:6379 | Redis缓存 |
| kafka | c2c-kafka | 29092:29092 | Kafka消息队列 |
| zookeeper | c2c-zookeeper | 2181:2181 | Zookeeper注册中心 |

## 访问地址

### 应用访问地址
- **C2C应用**: http://localhost:8094/c2c
- **C2G应用**: http://localhost:8096/c2g
- **Chat应用**: http://localhost:8095/chat
- **Gateway-TCP**: http://localhost:9094
- **Route服务**: http://localhost:9097
- **ID-Server**: http://localhost:8100/id-server

### 健康检查地址
- **C2C**: http://localhost:8094/c2c/actuator/health
- **C2G**: http://localhost:8096/c2g/actuator/health
- **Chat**: http://localhost:8095/chat/actuator/health
- **Gateway-TCP**: http://localhost:9094/actuator/health
- **Route**: http://localhost:9097/actuator/health
- **ID-Server**: http://localhost:8100/id-server/actuator/health

### 监控指标地址
- **C2C**: http://localhost:8094/c2c/actuator/prometheus
- **C2G**: http://localhost:8096/c2g/actuator/prometheus
- **Chat**: http://localhost:8095/chat/actuator/prometheus
- **Gateway-TCP**: http://localhost:9094/actuator/prometheus
- **Route**: http://localhost:9097/actuator/prometheus
- **ID-Server**: http://localhost:8100/id-server/actuator/prometheus

## 常用命令

### 脚本命令

```bash
# 查看帮助
./build-and-deploy.sh help

# 构建应用
./build-and-deploy.sh build

# 启动服务
./build-and-deploy.sh start

# 停止服务
./build-and-deploy.sh stop

# 重启服务
./build-and-deploy.sh restart

# 查看C2C应用日志
./build-and-deploy.sh logs

# 查看特定服务日志
./build-and-deploy.sh logs mysql
./build-and-deploy.sh logs mongodb

# 清理所有资源
./build-and-deploy.sh clean
```

### Docker Compose命令

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f c2c-app

# 进入容器
docker-compose exec c2c-app sh
docker-compose exec mysql mysql -u root -p

# 重新构建并启动
docker-compose up -d --build
```

## 环境变量配置

可以通过环境变量自定义配置：

```bash
# 数据库配置
export MYSQL_HOST=mysql
export MYSQL_PORT=3306
export MYSQL_DATABASE=wownowim_docker
export MYSQL_USERNAME=wownowim_docker
export MYSQL_PASSWORD=wownowim_docker_2025

# MongoDB配置
export MONGODB_HOST=mongodb
export MONGODB_PORT=27017
export MONGODB_DATABASE=REPORT_BEHAVIOR

# Redis配置
export REDIS_HOST=redis
export REDIS_PORT=6379

# Kafka配置
export KAFKA_BOOTSTRAP_SERVERS=kafka:9092

# 启动服务
docker-compose up -d
```

## 数据持久化

以下数据将持久化存储：

- **MySQL数据**: `mysql_data` 卷
- **MongoDB数据**: `mongodb_data` 卷
- **Redis数据**: `redis_data` 卷
- **Kafka数据**: `kafka_data` 卷
- **Zookeeper数据**: `zookeeper_data` 和 `zookeeper_logs` 卷
- **应用日志**: `c2c_logs` 卷

## 故障排除

### 1. 端口冲突

如果遇到端口冲突，可以修改 `docker-compose.yml` 中的端口映射：

```yaml
services:
  c2c-app:
    ports:
      - "18094:8094"  # 修改外部端口
      - "30886:20886"
```

### 2. 内存不足

如果容器启动失败，可能是内存不足。可以调整JVM参数：

```yaml
services:
  c2c-app:
    environment:
      JAVA_OPTS: "-Xms256m -Xmx512m"  # 减少内存使用
```

### 3. 服务启动顺序

如果C2C应用启动时依赖服务还未就绪，可以增加等待时间：

```yaml
services:
  c2c-app:
    depends_on:
      - mysql
      - mongodb
      - redis
      - kafka
    restart: on-failure
```

### 4. 查看详细日志

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务的详细日志
docker-compose logs -f --tail=100 c2c-app

# 进入容器查看
docker-compose exec c2c-app sh
tail -f /app/logs/c2c.log
```

## 生产环境部署建议

1. **资源限制**: 为容器设置CPU和内存限制
2. **健康检查**: 配置适当的健康检查参数
3. **日志管理**: 使用日志收集系统（如ELK）
4. **监控**: 集成Prometheus和Grafana
5. **备份**: 定期备份数据库和重要数据
6. **安全**: 使用非root用户运行容器，配置网络安全策略

## 更新部署

```bash
# 1. 停止服务
./build-and-deploy.sh stop

# 2. 拉取最新代码
git pull

# 3. 重新构建和部署
./build-and-deploy.sh deploy
```

## 联系支持

如果遇到问题，请检查：

1. Docker和Docker Compose版本是否符合要求
2. 端口是否被占用
3. 磁盘空间是否充足
4. 网络连接是否正常

更多问题请查看应用日志或联系开发团队。
