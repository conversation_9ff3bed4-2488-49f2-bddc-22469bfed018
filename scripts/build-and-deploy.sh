#!/bin/bash

# C2C模块Docker构建和部署脚本
# 使用方法: ./build-and-deploy.sh [build|start|stop|restart|logs|clean]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker和Docker Compose是否安装
check_prerequisites() {
    log_step "检查前置条件..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 构建所有模块
build_all_modules() {
    log_step "构建所有模块..."

    # 检查Maven是否安装
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi

    # 构建整个项目
    log_info "开始Maven构建..."
    mvn clean package -DskipTests

    if [ $? -eq 0 ]; then
        log_info "所有模块构建成功"
    else
        log_error "模块构建失败"
        exit 1
    fi
}

# 构建单个模块
build_module() {
    local module=$1
    log_step "构建${module}模块..."

    # 检查Maven是否安装
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi

    # 构建指定模块及其依赖
    log_info "开始Maven构建${module}模块..."
    mvn clean package -DskipTests -pl ${module} -am

    if [ $? -eq 0 ]; then
        log_info "${module}模块构建成功"
    else
        log_error "${module}模块构建失败"
        exit 1
    fi
}

# 创建必要的目录和文件
setup_directories() {
    log_step "创建必要的目录..."
    
    # 创建Docker相关目录
    mkdir -p docker/mysql/init
    mkdir -p docker/mongodb/init
    
    # 创建MySQL初始化脚本（如果不存在）
    if [ ! -f "docker/mysql/init/init.sql" ]; then
        cat > docker/mysql/init/init.sql << 'EOF'
-- 多模块数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS wownowim_docker CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE wownowim_docker;

-- ID生成器相关表（用于id-server模块）
CREATE TABLE IF NOT EXISTS camellia_id_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tag VARCHAR(255) NOT NULL UNIQUE COMMENT '业务标识',
    max_id BIGINT NOT NULL DEFAULT 0 COMMENT '当前最大ID',
    step INT NOT NULL DEFAULT 1000 COMMENT '步长',
    info VARCHAR(255) COMMENT '描述信息',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tag (tag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ID生成器配置表';

-- 插入默认的ID生成器配置
INSERT IGNORE INTO camellia_id_info (tag, max_id, step, info) VALUES
('c2c_message', 0, 1000, 'C2C消息ID'),
('c2g_message', 0, 1000, 'C2G消息ID'),
('chat_session', 0, 1000, '聊天会话ID'),
('user_id', 0, 1000, '用户ID');

-- 示例：消息表（根据实际需要修改）
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT PRIMARY KEY COMMENT '消息ID',
    from_user_id BIGINT NOT NULL COMMENT '发送者ID',
    to_user_id BIGINT COMMENT '接收者ID（C2C消息）',
    group_id BIGINT COMMENT '群组ID（C2G消息）',
    message_type TINYINT NOT NULL DEFAULT 1 COMMENT '消息类型：1-文本，2-图片，3-语音',
    content TEXT COMMENT '消息内容',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '消息状态：0-发送中，1-已发送，2-已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_from_user (from_user_id),
    INDEX idx_to_user (to_user_id),
    INDEX idx_group (group_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息表';

-- 示例：用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    nickname VARCHAR(100) COMMENT '昵称',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(255) COMMENT '头像URL',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入测试用户
INSERT IGNORE INTO users (id, username, nickname, email) VALUES
(1, 'admin', '管理员', '<EMAIL>'),
(2, 'test1', '测试用户1', '<EMAIL>'),
(3, 'test2', '测试用户2', '<EMAIL>');

EOF
        log_info "创建了MySQL初始化脚本"
    fi
    
    # 创建MongoDB初始化脚本（如果不存在）
    if [ ! -f "docker/mongodb/init/init.js" ]; then
        cat > docker/mongodb/init/init.js << 'EOF'
// MongoDB初始化脚本
// 创建数据库和用户

db = db.getSiblingDB('REPORT_BEHAVIOR');

// 创建用户
db.createUser({
    user: 'report_behavior',
    pwd: 'report_behavior_2020',
    roles: [
        {
            role: 'readWrite',
            db: 'REPORT_BEHAVIOR'
        }
    ]
});

// 创建集合（如果需要）
// db.createCollection('messages');

EOF
        log_info "创建了MongoDB初始化脚本"
    fi
}

# 启动服务
start_services() {
    log_step "启动Docker服务..."
    
    setup_directories
    
    # 启动所有服务
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_info "服务启动成功"
        log_info "应用访问地址:"
        log_info "  C2C应用: http://localhost:8094/c2c"
        log_info "  C2G应用: http://localhost:8096/c2g"
        log_info "  Chat应用: http://localhost:8095/chat"
        log_info "  Gateway-TCP: http://localhost:9094"
        log_info "  Route服务: http://localhost:9097"
        log_info "  ID-Server: http://localhost:8100/id-server"
        log_info ""
        log_info "健康检查地址:"
        log_info "  C2C: http://localhost:8094/c2c/actuator/health"
        log_info "  C2G: http://localhost:8096/c2g/actuator/health"
        log_info "  Chat: http://localhost:8095/chat/actuator/health"
        log_info "  Gateway-TCP: http://localhost:9094/actuator/health"
        log_info "  Route: http://localhost:9097/actuator/health"
        log_info "  ID-Server: http://localhost:8100/id-server/actuator/health"
        
        # 等待服务启动
        log_info "等待服务启动完成..."
        sleep 30
        
        # 检查服务状态
        docker-compose ps
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_step "停止Docker服务..."
    docker-compose down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_step "重启Docker服务..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    log_step "显示服务日志..."
    if [ -n "$2" ]; then
        docker-compose logs -f "$2"
    else
        docker-compose logs -f c2c-app
    fi
}

# 清理资源
clean_resources() {
    log_step "清理Docker资源..."
    
    read -p "确定要清理所有Docker资源吗？这将删除所有容器、镜像和数据卷 (y/N): " confirm
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        docker-compose down -v --rmi all
        docker system prune -f
        log_info "资源清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "C2C模块Docker部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 build [module] - 构建模块（不指定则构建所有模块）"
    echo "  $0 start          - 启动生产环境服务"
    echo "  $0 stop           - 停止所有服务"
    echo "  $0 restart        - 重启所有服务"
    echo "  $0 logs [service] - 查看日志（默认查看所有应用日志）"
    echo "  $0 clean          - 清理所有Docker资源"
    echo "  $0 deploy         - 完整部署（构建+启动）"
    echo "  $0 dev            - 启动开发环境（热启动模式）"
    echo ""
    echo "可用模块: c2c, c2g, chat, gateway-tcp, route, id-server"
    echo ""
    echo "示例:"
    echo "  $0 deploy         - 完整部署所有模块（生产模式）"
    echo "  $0 dev            - 启动开发环境（热启动模式）"
    echo "  $0 build c2c      - 只构建C2C模块"
    echo "  $0 logs c2c-app   - 查看C2C应用日志"
    echo "  $0 logs mysql     - 查看MySQL日志"
    echo ""
    echo "开发模式:"
    echo "  使用 ./dev-hot-reload.sh 脚本进行热启动开发"
    echo "  详细说明请查看 HOT_RELOAD_GUIDE.md"
}

# 完整部署
full_deploy() {
    log_step "开始完整部署..."
    check_prerequisites
    build_all_modules
    start_services
    log_info "生产环境部署完成！"
}

# 启动开发环境
start_dev_env() {
    log_step "启动开发环境..."
    log_info "使用热启动脚本启动开发环境"

    if [ ! -f "./dev-hot-reload.sh" ]; then
        log_error "找不到dev-hot-reload.sh脚本"
        exit 1
    fi

    ./dev-hot-reload.sh start
}

# 主函数
main() {
    case "${1:-help}" in
        "build")
            check_prerequisites
            if [ -n "$2" ]; then
                build_module "$2"
            else
                build_all_modules
            fi
            ;;
        "start")
            check_prerequisites
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs "$@"
            ;;
        "clean")
            clean_resources
            ;;
        "deploy")
            full_deploy
            ;;
        "dev")
            start_dev_env
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
