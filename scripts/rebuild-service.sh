#!/bin/bash

# 重建指定服务的脚本，使用版本标签避免缓存问题
# 用法: ./rebuild-service.sh <service-name> [build-version]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 帮助信息
show_help() {
    echo "用法: $0 <service-name> [build-version]"
    echo ""
    echo "参数:"
    echo "  service-name    服务名称 (c2c, c2g, chat, gateway-tcp, route, id-server, all)"
    echo "  build-version   构建版本 (可选，默认为当前时间戳)"
    echo ""
    echo "示例:"
    echo "  $0 c2g                    # 重建c2g服务"
    echo "  $0 c2g v1.0.1            # 重建c2g服务并指定版本"
    echo "  $0 all                   # 重建所有服务"
    echo ""
    echo "支持的服务:"
    echo "  - c2c"
    echo "  - c2g"
    echo "  - chat"
    echo "  - gateway-tcp"
    echo "  - route"
    echo "  - id-server"
    echo "  - all (重建所有服务)"
}

# 检查参数
if [ $# -lt 1 ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

SERVICE=$1
BUILD_VERSION=${2:-$(date +%Y%m%d%H%M%S)}
BUILD_DATE=$(date +%Y%m%d%H%M%S)

# 支持的服务列表
SERVICES=("c2c" "c2g" "chat" "gateway-tcp" "route" "id-server")

# 验证服务名称
validate_service() {
    local service=$1
    if [ "$service" = "all" ]; then
        return 0
    fi
    
    for valid_service in "${SERVICES[@]}"; do
        if [ "$service" = "$valid_service" ]; then
            return 0
        fi
    done
    
    echo -e "${RED}错误: 不支持的服务名称 '$service'${NC}"
    echo "支持的服务: ${SERVICES[*]} all"
    exit 1
}

# 重建单个服务
rebuild_single_service() {
    local service=$1
    local service_name="${service}-app-dev"
    local image_name="keep-alive-con-${service}-app-dev"
    
    echo -e "${BLUE}开始重建服务: $service${NC}"
    echo -e "${YELLOW}构建版本: $BUILD_VERSION${NC}"
    echo -e "${YELLOW}构建时间: $BUILD_DATE${NC}"
    
    # 停止服务
    echo -e "${YELLOW}停止服务 $service_name...${NC}"
    docker-compose -f docker-compose.dev.yml stop $service_name || true
    
    # 删除旧镜像
    echo -e "${YELLOW}删除旧镜像...${NC}"
    docker rmi $(docker images "${image_name}" -q) 2>/dev/null || true
    
    # 设置环境变量并重建
    echo -e "${YELLOW}重建镜像 (无缓存)...${NC}"
    export BUILD_DATE=$BUILD_DATE
    export BUILD_VERSION=$BUILD_VERSION
    
    docker-compose -f docker-compose.dev.yml build --no-cache --force-rm $service_name
    
    # 启动服务
    echo -e "${YELLOW}启动服务 $service_name...${NC}"
    docker-compose -f docker-compose.dev.yml up -d $service_name
    
    echo -e "${GREEN}✅ 服务 $service 重建完成${NC}"
    echo -e "${BLUE}镜像标签: ${image_name}:${BUILD_DATE}${NC}"
    echo ""
}

# 重建所有服务
rebuild_all_services() {
    echo -e "${BLUE}开始重建所有服务...${NC}"
    echo -e "${YELLOW}构建版本: $BUILD_VERSION${NC}"
    echo -e "${YELLOW}构建时间: $BUILD_DATE${NC}"
    echo ""
    
    for service in "${SERVICES[@]}"; do
        rebuild_single_service $service
        echo -e "${YELLOW}等待 3 秒后继续下一个服务...${NC}"
        sleep 3
    done
    
    echo -e "${GREEN}🎉 所有服务重建完成！${NC}"
}

# 显示服务状态
show_status() {
    echo -e "${BLUE}当前服务状态:${NC}"
    docker-compose -f docker-compose.dev.yml ps
    echo ""
    echo -e "${BLUE}镜像信息:${NC}"
    docker images | grep "keep-alive-con.*app-dev" | head -10
}

# 主逻辑
validate_service $SERVICE

echo -e "${GREEN}=== Docker 服务重建工具 ===${NC}"
echo -e "${BLUE}工作目录: $(pwd)${NC}"
echo ""

if [ "$SERVICE" = "all" ]; then
    rebuild_all_services
else
    rebuild_single_service $SERVICE
fi

echo -e "${YELLOW}重建完成，显示当前状态:${NC}"
show_status

echo -e "${GREEN}✨ 重建操作完成！${NC}"
echo -e "${BLUE}提示: 使用版本标签 $BUILD_DATE 可以确保镜像不会被缓存${NC}"
