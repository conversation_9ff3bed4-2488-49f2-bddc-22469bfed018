#!/bin/bash

# Docker热启动开发脚本
# 使用方法: ./dev-hot-reload.sh [command] [module]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

# 可用模块列表
MODULES=("c2c" "c2g" "chat" "gateway-tcp" "route" "id-server")

# 性能统计变量
SKIP_COUNT=0
COPY_COUNT=0

# 检查前置条件
check_prerequisites() {
    log_step "检查前置条件..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 检查依赖是否需要更新
check_dependencies_changed() {
    local module=$1
    local pom_file="${module}/pom.xml"
    local dep_cache_file="${module}/.dep-cache"
    local lib_dir="${module}/target/lib"

    # 如果lib目录不存在，需要复制依赖
    if [ ! -d "$lib_dir" ]; then
        return 0  # 需要更新
    fi

    # 如果缓存文件不存在，需要复制依赖
    if [ ! -f "$dep_cache_file" ]; then
        return 0  # 需要更新
    fi

    # 检查pom.xml是否比缓存文件新
    if [ "$pom_file" -nt "$dep_cache_file" ]; then
        return 0  # 需要更新
    fi

    # 检查父pom.xml是否比缓存文件新
    if [ "pom.xml" -nt "$dep_cache_file" ]; then
        return 0  # 需要更新
    fi

    return 1  # 不需要更新
}

# 智能复制依赖（优化版）
copy_dependencies_smart() {
    local module=$1
    local lib_dir="${module}/target/lib"
    local dep_cache_file="${module}/.dep-cache"

    # 检查是否需要复制依赖
    if check_dependencies_changed "$module"; then
        log_info "检测到依赖变化，复制${module}模块的依赖..."

        # 创建lib目录
        mkdir -p "$lib_dir"

        # 复制依赖（使用更快的方式）
        if mvn dependency:copy-dependencies -DoutputDirectory="$lib_dir" -pl "$module" -q -Dsilent=true; then
            # 更新缓存时间戳
            touch "$dep_cache_file"
            log_info "✅ ${module}模块依赖复制完成"
            ((COPY_COUNT++))
            return 0
        else
            log_error "❌ ${module}模块依赖复制失败"
            return 1
        fi
    else
        # 即使依赖没变化，也要确保lib目录存在且有文件
        if [ ! -d "$lib_dir" ] || [ -z "$(ls -A $lib_dir 2>/dev/null)" ]; then
            log_info "${module}模块依赖目录缺失，重新复制..."

            mkdir -p "$lib_dir"
            if mvn dependency:copy-dependencies -DoutputDirectory="$lib_dir" -pl "$module" -q -Dsilent=true; then
                touch "$dep_cache_file"
                log_info "✅ ${module}模块依赖复制完成"
                ((COPY_COUNT++))
                return 0
            else
                log_error "❌ ${module}模块依赖复制失败"
                return 1
            fi
        else
            log_debug "${module}模块依赖无变化，跳过复制"
            ((SKIP_COUNT++))
            return 0
        fi
    fi
}

# 快速编译（只编译Java文件）
compile_fast() {
    local module=$1
    log_info "快速编译${module}模块的class文件..."

    # 只编译，不处理依赖
    if mvn compile -pl "$module" -am -q -Dsilent=true -T 1C; then
        log_debug "✅ ${module}模块编译成功"
        return 0
    else
        log_error "❌ ${module}模块编译失败"
        return 1
    fi
}

# 编译单个模块（优化版）
compile_module() {
    local module=$1
    log_step "编译${module}模块..."

    if [[ ! " ${MODULES[@]} " =~ " ${module} " ]]; then
        log_error "无效的模块名: ${module}"
        log_info "可用模块: ${MODULES[*]}"
        exit 1
    fi

    # 快速编译Java文件
    if ! compile_fast "$module"; then
        log_error "${module}模块编译失败"
        exit 1
    fi

    # 智能复制依赖
    if ! copy_dependencies_smart "$module"; then
        log_error "${module}模块依赖处理失败"
        exit 1
    fi

    log_info "✅ ${module}模块编译成功"
}

# 初始化全局依赖缓存
init_global_cache() {
    log_step "初始化全局依赖缓存..."

    local cache_dir=".dev-cache"
    mkdir -p "$cache_dir"

    # 创建全局依赖目录
    local global_lib_dir="$cache_dir/lib"
    mkdir -p "$global_lib_dir"

    # 如果全局缓存为空，复制一次所有依赖
    if [ ! -f "$cache_dir/.initialized" ] || [ -z "$(ls -A $global_lib_dir 2>/dev/null)" ]; then
        log_info "首次初始化，复制所有依赖到全局缓存..."

        # 复制所有依赖到全局缓存
        mvn dependency:copy-dependencies -DoutputDirectory="$global_lib_dir" -q -Dsilent=true

        # 标记已初始化
        touch "$cache_dir/.initialized"

        log_info "✅ 全局依赖缓存初始化完成"
    else
        log_debug "全局依赖缓存已存在，跳过初始化"
    fi
}

# 检查模块依赖是否需要从全局缓存更新
need_copy_from_global_cache() {
    local module=$1
    local lib_dir="${module}/target/lib"
    local dep_cache_file="${module}/.dep-cache"
    local cache_dir=".dev-cache"
    local global_cache_file="$cache_dir/.initialized"

    # 如果模块lib目录不存在，需要复制
    if [ ! -d "$lib_dir" ]; then
        return 0  # 需要复制
    fi

    # 如果模块lib目录为空，需要复制
    if [ -z "$(ls -A $lib_dir 2>/dev/null)" ]; then
        return 0  # 需要复制
    fi

    # 如果模块依赖缓存不存在，需要复制
    if [ ! -f "$dep_cache_file" ]; then
        return 0  # 需要复制
    fi

    # 如果全局缓存比模块缓存新，需要复制
    if [ -f "$global_cache_file" ] && [ "$global_cache_file" -nt "$dep_cache_file" ]; then
        return 0  # 需要复制
    fi

    return 1  # 不需要复制
}

# 从全局缓存复制依赖（优化版）
copy_from_global_cache() {
    local module=$1
    local lib_dir="${module}/target/lib"
    local cache_dir=".dev-cache"
    local global_lib_dir="$cache_dir/lib"

    # 检查全局缓存是否可用
    if [ ! -d "$global_lib_dir" ] || [ -z "$(ls -A $global_lib_dir 2>/dev/null)" ]; then
        return 1  # 全局缓存不可用
    fi

    # 检查是否需要复制
    if ! need_copy_from_global_cache "$module"; then
        log_debug "${module}模块依赖无变化，跳过从全局缓存复制"
        ((SKIP_COUNT++))
        return 0  # 跳过复制，但返回成功
    fi

    log_info "从全局缓存复制${module}模块依赖..."

    mkdir -p "$lib_dir"

    # 使用rsync或cp快速复制（如果有rsync用rsync，更快）
    if command -v rsync &> /dev/null; then
        rsync -a --delete "$global_lib_dir/" "$lib_dir/"
    else
        rm -rf "$lib_dir"
        cp -r "$global_lib_dir" "$lib_dir"
    fi

    log_info "✅ ${module}模块依赖从缓存复制完成"
    ((COPY_COUNT++))
    return 0
}

# 编译所有模块（优化版）
compile_all() {
    log_step "编译所有模块..."

    # 重置统计计数器
    SKIP_COUNT=0
    COPY_COUNT=0

    # 初始化全局缓存
    init_global_cache

    # 并行编译所有模块的Java文件
    log_info "并行编译所有模块的Java文件..."
    mvn compile -T 1C -q -Dsilent=true

    # 为每个模块复制依赖
    for module in "${MODULES[@]}"; do
        if ! copy_from_global_cache "$module"; then
            # 如果全局缓存失败，回退到单独复制
            copy_dependencies_smart "$module"
        fi

        # 更新依赖缓存时间戳
        touch "${module}/.dep-cache"
    done

    # 显示性能统计
    local total_modules=${#MODULES[@]}
    log_info "所有模块编译完成"
    log_info "📊 性能统计: 跳过复制 ${SKIP_COUNT}/${total_modules} 个模块，实际复制 ${COPY_COUNT}/${total_modules} 个模块"

    if [ $SKIP_COUNT -gt 0 ]; then
        local skip_percentage=$(( SKIP_COUNT * 100 / total_modules ))
        log_info "🚀 优化效果: 跳过了 ${skip_percentage}% 的依赖复制操作"
    fi
}

# 启动开发环境
start_dev() {
    log_step "启动开发环境..."
    
    # 确保编译了所有模块
    compile_all
    
    # 启动开发环境
    log_info "启动Docker开发环境..."
    docker-compose -f docker-compose.dev.yml up -d
    
    if [ $? -eq 0 ]; then
        log_info "开发环境启动成功"
        show_dev_info
    else
        log_error "开发环境启动失败"
        exit 1
    fi
}

# 停止开发环境（只停止应用模块，保留基础设施）
stop_dev() {
    log_step "停止应用模块..."

    # 定义应用模块服务名
    local app_services=(
        "c2c-app-dev"
        "c2g-app-dev"
        "chat-app-dev"
        "gateway-tcp-app-dev"
        "route-app-dev"
        "id-server-app-dev"
    )

    # 停止应用服务
    for service in "${app_services[@]}"; do
        log_info "停止 ${service}..."
        docker-compose -f docker-compose.dev.yml stop "$service"
    done

    # 删除应用容器（保留基础设施容器）
    for service in "${app_services[@]}"; do
        docker-compose -f docker-compose.dev.yml rm -f "$service"
    done

    log_info "✅ 应用模块已停止，基础设施服务继续运行"
    log_info "💡 基础设施服务（MySQL、Redis、Kafka等）仍在运行"
}

# 完全停止开发环境（包括基础设施）
stop_all() {
    log_step "完全停止开发环境..."
    docker-compose -f docker-compose.dev.yml down
    log_info "✅ 开发环境已完全停止"
}

# 重启开发环境
restart_dev() {
    log_step "重启开发环境..."
    stop_dev
    start_dev
}

# 快速重载（只编译Java，不处理依赖）
reload_fast() {
    local module=$1
    log_step "快速重载${module}模块..."

    # 只编译Java文件
    compile_fast "$module"

    # 重启容器以加载新的class文件
    log_info "重启${module}容器..."
    docker-compose -f docker-compose.dev.yml restart ${module}-app-dev

    log_info "${module}模块快速重载完成"
}

# 重新加载单个模块（完整版）
reload_module() {
    local module=$1
    log_step "热重载${module}模块..."

    # 编译模块（包括依赖检查）
    compile_module "$module"

    # 重启容器以加载新的class文件
    log_info "重启${module}容器..."
    docker-compose -f docker-compose.dev.yml restart ${module}-app-dev

    log_info "${module}模块热重载完成"
}

# 查看开发环境日志
show_logs() {
    local module=${1:-""}
    
    if [ -n "$module" ]; then
        if [[ ! " ${MODULES[@]} " =~ " ${module} " ]]; then
            log_error "无效的模块名: ${module}"
            log_info "可用模块: ${MODULES[*]}"
            exit 1
        fi
        log_info "显示${module}模块日志..."
        docker-compose -f docker-compose.dev.yml logs -f ${module}-app-dev
    else
        log_info "显示所有应用日志..."
        docker-compose -f docker-compose.dev.yml logs -f c2c-app-dev c2g-app-dev chat-app-dev gateway-tcp-app-dev route-app-dev id-server-app-dev
    fi
}

# 显示开发环境信息
show_dev_info() {
    log_info "开发环境信息:"
    echo ""
    echo "📱 应用访问地址:"
    echo "  C2C应用: http://localhost:8094/c2c"
    echo "  C2G应用: http://localhost:8096/c2g"
    echo "  Chat应用: http://localhost:8095/chat"
    echo "  Gateway-TCP: http://localhost:9094"
    echo "  Route服务: http://localhost:9097"
    echo "  ID-Server: http://localhost:8100/id-server"
    echo ""
    echo "🐛 远程调试端口:"
    echo "  C2C: localhost:5005"
    echo "  C2G: localhost:5006"
    echo "  Chat: localhost:5007"
    echo "  Gateway-TCP: localhost:5008"
    echo "  Route: localhost:5009"
    echo "  ID-Server: localhost:5010"
    echo ""
    echo "💾 数据库连接:"
    echo "  MySQL: localhost:3306 (用户: wownowim_docker, 密码: wownowim_docker_2025)"
    echo "  MongoDB: localhost:27017 (用户: admin, 密码: admin123456)"
    echo "  Redis: localhost:6379"
    echo ""
    echo "🔄 热重载命令:"
    echo "  重载单个模块: ./dev-hot-reload.sh reload <module>"
    echo "  查看日志: ./dev-hot-reload.sh logs [module]"
    echo "  重启环境: ./dev-hot-reload.sh restart"
}

# 监控文件变化并自动重载
watch_and_reload() {
    local module=${1:-""}
    
    if [ -n "$module" ]; then
        if [[ ! " ${MODULES[@]} " =~ " ${module} " ]]; then
            log_error "无效的模块名: ${module}"
            log_info "可用模块: ${MODULES[*]}"
            exit 1
        fi
        log_info "监控${module}模块文件变化..."
        watch_module "$module"
    else
        log_info "监控所有模块文件变化..."
        for mod in "${MODULES[@]}"; do
            watch_module "$mod" &
        done
        wait
    fi
}

# 监控单个模块
watch_module() {
    local module=$1
    local watch_dir="${module}/src/main/java"
    
    if command -v fswatch &> /dev/null; then
        log_info "使用fswatch监控${module}模块..."
        fswatch -o "$watch_dir" | while read f; do
            log_info "检测到${module}模块文件变化，开始热重载..."
            reload_module "$module"
        done
    elif command -v inotifywait &> /dev/null; then
        log_info "使用inotifywait监控${module}模块..."
        inotifywait -m -r -e modify,create,delete "$watch_dir" | while read path action file; do
            if [[ "$file" == *.java ]]; then
                log_info "检测到${module}模块Java文件变化: $file，开始热重载..."
                reload_module "$module"
            fi
        done
    else
        log_warn "未找到文件监控工具(fswatch或inotifywait)，请手动执行热重载"
        log_info "安装方法:"
        log_info "  macOS: brew install fswatch"
        log_info "  Linux: sudo apt-get install inotify-tools"
    fi
}

# 清理依赖缓存
clean_cache() {
    log_step "清理依赖缓存..."

    read -p "确定要清理所有依赖缓存吗？(y/N): " confirm
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        # 清理全局缓存
        rm -rf .dev-cache

        # 清理各模块缓存
        for module in "${MODULES[@]}"; do
            rm -f "${module}/.dep-cache"
            rm -rf "${module}/target/lib"
        done

        log_info "依赖缓存清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示缓存状态
show_cache_status() {
    log_step "显示缓存状态..."

    echo ""
    echo "📦 全局依赖缓存:"
    if [ -d ".dev-cache/lib" ]; then
        local cache_size=$(du -sh .dev-cache/lib 2>/dev/null | cut -f1)
        local cache_count=$(find .dev-cache/lib -name "*.jar" 2>/dev/null | wc -l)
        echo "  ✅ 已初始化 (大小: $cache_size, JAR文件: $cache_count 个)"
    else
        echo "  ❌ 未初始化"
    fi

    echo ""
    echo "📁 模块依赖缓存:"
    for module in "${MODULES[@]}"; do
        if [ -f "${module}/.dep-cache" ]; then
            local cache_time=$(stat -f "%Sm" "${module}/.dep-cache" 2>/dev/null || stat -c "%y" "${module}/.dep-cache" 2>/dev/null)
            echo "  ✅ ${module} (更新时间: $cache_time)"
        else
            echo "  ❌ ${module} (未缓存)"
        fi
    done

    echo ""
    echo "💾 磁盘使用情况:"
    if [ -d ".dev-cache" ]; then
        echo "  全局缓存: $(du -sh .dev-cache 2>/dev/null | cut -f1)"
    fi

    for module in "${MODULES[@]}"; do
        if [ -d "${module}/target" ]; then
            echo "  ${module}/target: $(du -sh ${module}/target 2>/dev/null | cut -f1)"
        fi
    done
}

# 显示性能分析
show_performance_analysis() {
    log_step "性能分析报告..."

    echo ""
    echo "🚀 编译优化分析:"

    # 检查每个模块的优化状态
    local optimized_count=0
    local total_count=${#MODULES[@]}

    for module in "${MODULES[@]}"; do
        echo ""
        echo "📋 ${module} 模块:"

        # 检查pom.xml和缓存时间
        local pom_file="${module}/pom.xml"
        local dep_cache_file="${module}/.dep-cache"
        local lib_dir="${module}/target/lib"

        if [ -f "$dep_cache_file" ]; then
            if [ "$pom_file" -nt "$dep_cache_file" ]; then
                echo "  ⚠️  依赖需要更新 (pom.xml已修改)"
            else
                echo "  ✅ 依赖已优化 (无需重新复制)"
                ((optimized_count++))
            fi
        else
            echo "  ❌ 未缓存 (首次编译需要复制依赖)"
        fi

        # 检查lib目录状态
        if [ -d "$lib_dir" ]; then
            local jar_count=$(find "$lib_dir" -name "*.jar" 2>/dev/null | wc -l)
            local lib_size=$(du -sh "$lib_dir" 2>/dev/null | cut -f1)
            echo "  📦 依赖状态: $jar_count 个JAR文件, 大小: $lib_size"
        else
            echo "  📦 依赖状态: 未初始化"
        fi

        # 预测编译时间
        if [ -f "$dep_cache_file" ] && [ "$pom_file" -ot "$dep_cache_file" ] && [ -d "$lib_dir" ]; then
            echo "  ⏱️  预计编译时间: 5-10秒 (快速模式)"
        else
            echo "  ⏱️  预计编译时间: 20-30秒 (包含依赖复制)"
        fi
    done

    echo ""
    echo "📊 总体优化效果:"
    local optimization_rate=$(( optimized_count * 100 / total_count ))
    echo "  优化模块: ${optimized_count}/${total_count} (${optimization_rate}%)"

    if [ $optimization_rate -ge 80 ]; then
        echo "  🎉 优化效果: 优秀 - 大部分模块都能快速编译"
    elif [ $optimization_rate -ge 50 ]; then
        echo "  👍 优化效果: 良好 - 多数模块能快速编译"
    else
        echo "  💡 优化建议: 运行一次完整编译来初始化缓存"
    fi

    echo ""
    echo "💡 性能提升建议:"
    echo "  1. 使用 './dev-hot-reload.sh fast <module>' 进行快速重载"
    echo "  2. 只在修改pom.xml后使用完整重载"
    echo "  3. 定期运行 './dev-hot-reload.sh cache' 检查缓存状态"
    echo "  4. 使用 './benchmark-compile.sh all' 测试性能"
}

# 清理开发环境
clean_dev() {
    log_step "清理开发环境..."

    read -p "确定要清理开发环境的所有数据吗？(y/N): " confirm
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        docker-compose -f docker-compose.dev.yml down -v --rmi local
        log_info "开发环境清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "Docker热启动开发脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 start                    - 启动开发环境"
    echo "  $0 stop                     - 停止应用模块（保留基础设施）"
    echo "  $0 stop-all                 - 完全停止开发环境（包括基础设施）"
    echo "  $0 restart                  - 重启开发环境"
    echo "  $0 compile [module]         - 编译模块（不指定则编译所有）"
    echo "  $0 reload <module>          - 热重载指定模块（完整版）"
    echo "  $0 fast <module>            - 快速重载指定模块（仅Java文件）"
    echo "  $0 logs [module]            - 查看日志（不指定则查看所有应用日志）"
    echo "  $0 watch [module]           - 监控文件变化并自动重载"
    echo "  $0 info                     - 显示开发环境信息"
    echo "  $0 cache                    - 显示缓存状态"
    echo "  $0 perf                     - 显示性能分析"
    echo "  $0 clean-cache              - 清理依赖缓存"
    echo "  $0 clean                    - 清理开发环境"
    echo ""
    echo "可用模块: ${MODULES[*]}"
    echo ""
    echo "示例:"
    echo "  $0 start                    - 启动完整开发环境"
    echo "  $0 reload c2c               - 热重载C2C模块（完整版）"
    echo "  $0 fast c2c                 - 快速重载C2C模块（仅Java文件）"
    echo "  $0 logs c2c                 - 查看C2C模块日志"
    echo "  $0 watch c2c                - 监控C2C模块文件变化"
    echo "  $0 cache                    - 查看缓存状态"
    echo "  $0 perf                     - 查看性能分析"
    echo ""
    echo "💡 性能优化提示:"
    echo "  - 首次启动会初始化全局依赖缓存，后续编译会更快"
    echo "  - 使用 'fast' 命令进行快速重载，只编译Java文件"
    echo "  - 依赖缓存会自动检测pom.xml变化，无需手动清理"
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_prerequisites
            start_dev
            ;;
        "stop")
            stop_dev
            ;;
        "stop-all")
            stop_all
            ;;
        "restart")
            restart_dev
            ;;
        "compile")
            check_prerequisites
            if [ -n "$2" ]; then
                compile_module "$2"
            else
                compile_all
            fi
            ;;
        "reload")
            if [ -z "$2" ]; then
                log_error "请指定要重载的模块名"
                show_help
                exit 1
            fi
            check_prerequisites
            reload_module "$2"
            ;;
        "fast")
            if [ -z "$2" ]; then
                log_error "请指定要快速重载的模块名"
                show_help
                exit 1
            fi
            check_prerequisites
            reload_fast "$2"
            ;;
        "logs")
            show_logs "$2"
            ;;
        "watch")
            check_prerequisites
            watch_and_reload "$2"
            ;;
        "info")
            show_dev_info
            ;;
        "cache")
            show_cache_status
            ;;
        "perf")
            show_performance_analysis
            ;;
        "clean-cache")
            clean_cache
            ;;
        "clean")
            clean_dev
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
