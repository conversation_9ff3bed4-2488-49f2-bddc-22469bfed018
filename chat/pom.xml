<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chaos</groupId>
        <artifactId>keep-alive-con</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.chaos.im.chat</groupId>
    <artifactId>chat-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>


        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.21</version>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-base</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.16.3</version>
        </dependency>


        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>basic-common-api</artifactId>
            <optional>true</optional>
            <version>2.5.1.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>com.chaos.im.chat</groupId>
            <artifactId>chat-api</artifactId>
            <version>1.0.0.1-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.chaos.im.c2g</groupId>
            <artifactId>c2g-api</artifactId>
            <version>1.0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.chaos.im.c2c</groupId>
            <artifactId>c2c-api</artifactId>
            <version>1.0.0.1-SNAPSHOT</version>
        </dependency>





        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-container-dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>chaos-usercenter-api</artifactId>
            <version>5.4.8.9-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.22</version>
        </dependency>


        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>keep-alive-common-web</artifactId>
            <version>1.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>keep-alive-common-im</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.ttf</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <finalName>chat</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>



</project>