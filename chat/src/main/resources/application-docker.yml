go:
  mvc:
    responseFilter: /actuator/prometheus

dubbo:
  application:
    name: chat
    qos-port: 33333
  consumer:
    group: chaos
  protocol:
    port: 20882
  provider:
    group: chaos
  registry:
    address: zookeeper://${ZOOKEEPER_HOST:zookeeper}:${ZOOKEEPER_PORT:2181}
    check: false

server:
  port: 8095
  servlet:
    context-path: /chat

spring:
  application:
    name: chat
  kafka:
    consumer:
      auto-offset-reset: latest
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka:9092}
      enable-auto-commit: false
      group-id: chat
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      ack-mode: manual_immediate
      type: batch
    producer:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka:9092}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
  main:
    allow-bean-definition-overriding: true
    banner-mode: 'off'
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms

# 日志配置
logging:
  level:
    com.chaos.im.chat: INFO
    org.springframework.kafka: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/chat.log

# 监控端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
