<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
    http://code.alibabatech.com/schema/dubbo
    http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:service interface="com.chaos.im.chat.api.ChatApi" class="com.chaos.im.chat.impl.ChatApiImpl" version="1.0.0" >
        <dubbo:method name="addChat" timeout="100000"/>
        <dubbo:method name="getPeerIdByChatId" timeout="100000"/>
    </dubbo:service>



</beans>
