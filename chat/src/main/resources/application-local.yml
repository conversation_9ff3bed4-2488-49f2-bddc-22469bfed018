go:
    mvc:
        responseFilter: /actuator/prometheus
dubbo:
    application:
        name: chat
        qos-port: 33333
    consumer:
        group: chaos
    protocol:
        port: 20882
    provider:
        group: chaos
    registry:
        address: zookeeper://*************:2181
logging:
    level:
        root: INFO
mybatis-plus:
    configuration:
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
        banner: false
        db-config:
            id-type: auto
server:
    port: 8095
    servlet:
        context-path: /chat
spring:
    application:
        name: chat
    data:
        mongodb:
            database: REPORT_BEHAVIOR
            host: ************
            password: report_behavior_2020
            port: 27017
            username: report_behavior
    datasource:
        asyncInit: true
        driver-class-name: com.mysql.cj.jdbc.Driver
        filters: stat
        initialSize: 1
        maxActive: 3
        maxOpenPreparedStatements: 20
        maxWait: 60000
        minEvictableIdleTimeMillis: 300000
        minIdle: 1
        password: wownowim_sit_2025
        poolPreparedStatements: true
        testOnBorrow: false
        testOnReturn: false
        testWhileIdle: true
        timeBetweenEvictionRunsMillis: 60000
        url: ***************************************************************************************************************************************************************
        username: wownowim_sit
    kafka:
        consumer:
            auto-offset-reset: latest
            bootstrap-servers: ************:29092
            enable-auto-commit: false
            group-id: chat
            key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
            value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        listener:
            ack-mode: manual_immediate
            type: batch
        producer:
            bootstrap-servers: ************:29092
            key-serializer: org.apache.kafka.common.serialization.StringSerializer
            value-serializer: org.apache.kafka.common.serialization.StringSerializer
    main:
        allow-bean-definition-overriding: true
        banner-mode: 'off'
    redis:
        host: *************
        port: 6379
wownow:
    im:
        driver:
            ios:
                version: 5.2.8
            android:
                version: 1.2.8
        superapp:
            ios:
                version: 5.75.13
            android:
                version: 2.75.13