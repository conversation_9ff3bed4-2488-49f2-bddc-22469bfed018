package com.chaos.im.chat.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaos.im.c2c.api.C2cApi;
import com.chaos.im.c2c.api.domain.LastC2cMessageDTO;
import com.chaos.im.c2g.group.api.GroupApi;
import com.chaos.im.c2g.group.api.domain.LastC2gMessageDTO;
import com.chaos.im.chat.dao.ChatDAO;
import com.chaos.im.chat.dao.ChatMemberDAO;
import com.chaos.im.chat.domain.*;
import com.chaos.im.chat.service.ChatMemberService;
import com.chaos.im.chat.service.ChatService;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.resp.OperatorInfoRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatServiceImpl implements ChatService {

    @Autowired
    private ChatDAO chatDAO;

    @Autowired
    private ChatMemberDAO chatMemberDAO;

    @DubboReference(version = "1.0.0")
    private UserOperatorFacade userOperatorFacade;

    @Autowired
    private ChatMemberService chatMemberService;

    @DubboReference(version = "1.0.0")
    private C2cApi c2cApi;

    @DubboReference(version = "1.0.0")
    private GroupApi c2gApi;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 将Redis中的Map数据转换为LastMessageDTO对象
     */
    private LastMessageDTO convertMapToLastMessageDTO(Map<String, Object> lastMsgMap) {
        if (lastMsgMap == null || lastMsgMap.isEmpty()) {
            return new LastMessageDTO();
        }

        LastMessageDTO lastMessageDTO = new LastMessageDTO();

        // 安全地转换各个字段
        if (lastMsgMap.get("chatId") != null) {
            lastMessageDTO.setChatId(Long.valueOf(lastMsgMap.get("chatId").toString()));
        }
        if (lastMsgMap.get("messageId") != null) {
            lastMessageDTO.setMessageId(Long.valueOf(lastMsgMap.get("id").toString()));
        }
        if (lastMsgMap.get("fromOperatorNo") != null) {
            lastMessageDTO.setFromOperatorNo(lastMsgMap.get("fromOperatorNo").toString());
        }
        if (lastMsgMap.get("toOperatorNo") != null) {
            lastMessageDTO.setToOperatorNo(lastMsgMap.get("toOperatorNo").toString());
        }
        if (lastMsgMap.get("category") != null) {
            lastMessageDTO.setCategory(Integer.valueOf(lastMsgMap.get("category").toString()));
        }
        if (lastMsgMap.get("content") != null) {
            lastMessageDTO.setContent(lastMsgMap.get("content").toString());
        }
        if (lastMsgMap.get("appId") != null) {
            lastMessageDTO.setAppId(lastMsgMap.get("appId").toString());
        }
        if (lastMsgMap.get("sequence") != null) {
            lastMessageDTO.setSequence(Long.valueOf(lastMsgMap.get("sequence").toString()));
        }
        if (lastMsgMap.get("timestamp") != null) {
            lastMessageDTO.setTimestamp(Long.valueOf(lastMsgMap.get("timestamp").toString()));
        }

        return lastMessageDTO;
    }

    @Override
    public IPage<ChatMemberDTO> pageChat(ChatListRequest chatListRequest)  {
        StopWatch stopWatch = new StopWatch("pageChat");
//        QueryWrapper<ChatMemberDO> queryWrapper = new QueryWrapper<ChatMemberDO>();
//        queryWrapper.eq("member_id", chatListRequest.getOperatorNo());
//        if (ObjectUtil.isNotNull(chatListRequest.getChatType())) {
//            queryWrapper.eq("type", chatListRequest.getChatType());
//        }
//        queryWrapper.orderByDesc("gmt_create");

        stopWatch.start("pagpageByMemberIdeChat from DB, operatorNo:"+chatListRequest.getOperatorNo());
        IPage<ChatMemberDO> page = chatMemberService.pageByMemberId(chatListRequest.getOperatorNo(),
                chatListRequest.getCurrent(), chatListRequest.getPageSize());
        stopWatch.stop();
        List<String> c2cLastMessageKey = new ArrayList<>();
        List<String> c2cUnReadCountKey = new ArrayList<>();
        List<String> c2gLastMessageKey = new ArrayList<>();
        List<String> c2gUnReadCountKey = new ArrayList<>();
        RBatch batch = redissonClient.createBatch();
        Map<String, RFuture<Map<String, Object>>> lastMessageFuture = new HashMap<>();

        RBatch unReadCountBatch = redissonClient.createBatch();
        Map<String, RFuture<Long>> unReadCountFuture = new HashMap<>();

        page.getRecords().forEach(it->{
            if (Objects.equals(it.getType(), Constants.CHAT_TYPE_C2C)) {
                c2cLastMessageKey.add(ImConstants.C2C_LAST_MESSAGE_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                c2cUnReadCountKey.add(ImConstants.C2C_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                RMapAsync<String,Object> mapAsync = batch.getMap(ImConstants.C2C_LAST_MESSAGE_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                lastMessageFuture.put(ImConstants.C2C_LAST_MESSAGE_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo(),mapAsync.readAllMapAsync());
                RAtomicLongAsync longAsync = unReadCountBatch.getAtomicLong(ImConstants.C2C_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                unReadCountFuture.put(ImConstants.C2C_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo(),longAsync.getAsync());
            } else {
                c2gLastMessageKey.add(ImConstants.C2G_LAST_MESSAGE_PREFIX + it.getChatId());
                c2gUnReadCountKey.add(ImConstants.C2G_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                RMapAsync<String,Object> mapAsync = batch.getMap(ImConstants.C2G_LAST_MESSAGE_PREFIX + it.getChatId());
                lastMessageFuture.put(ImConstants.C2G_LAST_MESSAGE_PREFIX + it.getChatId(),mapAsync.readAllMapAsync());
                RAtomicLongAsync longAsync = unReadCountBatch.getAtomicLong(ImConstants.C2G_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                unReadCountFuture.put(ImConstants.C2G_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo(),longAsync.getAsync());
            }
        });

        batch.execute();

        unReadCountBatch.execute();

        // 获取结果集

        stopWatch.start("get last msg operatorNo:"+chatListRequest.getOperatorNo());
        Map<String, Map<String, Object>> lastMsgResults = new HashMap<>();
        for (Map.Entry<String, RFuture<Map<String, Object>>> entry : lastMessageFuture.entrySet()) {
            try {
                // 增加超时时间到1秒
                Map<String, Object> result = entry.getValue().get(1000, TimeUnit.MILLISECONDS);
                if (result != null) {
                    lastMsgResults.put(entry.getKey(), result);
                }
            }catch (TimeoutException e){
                log.warn("get lastMessage timeout for key:[{}]", entry.getKey());
            }catch (Exception e){
                log.error("get lastMessage error: [{}] key:[{}]", e.getMessage(), entry.getKey(), e);
            }
        }
        stopWatch.stop();

        stopWatch.start("get unReadCount operatorNo:"+chatListRequest.getOperatorNo());
        Map<String, Long> unReadCountResults = new HashMap<>();
        for (Map.Entry<String, RFuture<Long>> entry : unReadCountFuture.entrySet()) {
            try {
                // 增加超时时间到1秒，并提供默认值
                Long result = entry.getValue().get(1000, TimeUnit.MILLISECONDS);
                unReadCountResults.put(entry.getKey(), result != null ? result : 0L);
            }catch (TimeoutException e){
                log.warn("get unReadCount timeout, using default value 0 for key:[{}]", entry.getKey());
                unReadCountResults.put(entry.getKey(), 0L);
            }catch (Exception e){
                log.error("get unReadCount error: [{}] key:[{}], using default value 0", e.getMessage(), entry.getKey(), e);
                unReadCountResults.put(entry.getKey(), 0L);
            }
        }
        stopWatch.stop();

        stopWatch.start("convert to list , operatorNo:"+chatListRequest.getOperatorNo());
        List<ChatMemberDTO> chatMemberDTOList = page.getRecords().stream().map(it -> {
            ChatMemberDTO chatMemberDTO = it.clone(ChatMemberDTO.class);
            chatMemberDTO.setId(it.getChatId());
            LastMessageDTO lastMessageDTO = new LastMessageDTO();
            if (Objects.equals(it.getType(), Constants.CHAT_TYPE_C2C)) {
                Map<String,Object> lastMsgMap = lastMsgResults.get(ImConstants.C2C_LAST_MESSAGE_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                if (ObjectUtil.isNotNull(lastMsgMap)) {
                    log.info("get c2clastMsg from redis :{}", JSONUtil.toJsonStr(lastMsgMap));
                    lastMessageDTO = convertMapToLastMessageDTO(lastMsgMap);
                    chatMemberDTO.setLastMessage(lastMessageDTO);
                }

                Long unReadCount = unReadCountResults.get(ImConstants.C2C_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                lastMessageDTO.setUnReadCount((unReadCount == null ? 0 : unReadCount));
            } else {
                Map<String,Object> lastMsgMap = lastMsgResults.get(ImConstants.C2G_LAST_MESSAGE_PREFIX + it.getChatId());
                if (ObjectUtil.isNotNull(lastMsgMap)) {
                    log.info("get c2glastMsg from redis:{}", JSONUtil.toJsonStr(lastMsgMap));
                    lastMessageDTO = convertMapToLastMessageDTO(lastMsgMap);
                    chatMemberDTO.setLastMessage(lastMessageDTO);
                }
                Long unReadCount = unReadCountResults.get(ImConstants.C2G_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + chatListRequest.getOperatorNo());
                lastMessageDTO.setUnReadCount((unReadCount == null ? 0 : unReadCount));
            }
            return chatMemberDTO;
        }).collect(Collectors.toList());

        Page<ChatMemberDTO> resPage = new Page<>(page.getCurrent(), page.getSize());
        resPage.setTotal(page.getTotal());
        resPage.setRecords(chatMemberDTOList);
        stopWatch.stop();

        log.info(stopWatch.prettyPrint());

        return resPage;
    }

    public Long saveC2c(ChatDTO chatDTO, Long chatId) {
        log.info("创建会话并加入,chatDTO:{} chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        Optional<ChatMemberDO> optional;

        // 根据聊天类型获取会话信息
        log.info("单聊会话 chatDTO:{},chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        optional = chatMemberDAO.get(chatDTO.getType(), chatDTO.getMemberId(), chatDTO.getPeerId());

        if(optional.isPresent()){
            return optional.get().getChatId();
        }
        //单聊，且不存在会话，则创建会话
        if (chatId == null) {
            // 创建新的聊天会话
            ChatDO chatDO = new ChatDO();
            chatDO.setType(chatDTO.getType());
            chatId = chatDAO.save(chatDO);
        }

        // 创建聊天成员关系
        ChatMemberDO chatMemberDO = new ChatMemberDO();
        chatMemberDO.setType(chatDTO.getType());
        chatMemberDO.setChatId(chatId);
        chatMemberDO.setMemberId(chatDTO.getMemberId());
        chatMemberDO.setPeerId(chatDTO.getPeerId());
        chatMemberDO.setAvatar(chatDTO.getAvatar());
        chatMemberDO.setNickname(chatDTO.getNickname());
        chatMemberDO.setActiveTime(LocalDateTime.now());
        chatMemberDAO.save(chatMemberDO);

        return chatId;
    }

    public Long saveC2g(ChatDTO chatDTO, Long chatId) {
        log.info("创建会话并加入,chatDTO:{} chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        Optional<ChatMemberDO> optional;
        log.info("创建群聊会话 chatDTO:{},chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        optional = chatMemberDAO.get(chatDTO.getType(), chatDTO.getPeerId());

        if (optional.isPresent()) {
            chatId = optional.get().getChatId();
        }

        //群聊，且不存在会话,则创建会话
        if (chatId == null) {
            // 创建新的聊天会话
            ChatDO chatDO = new ChatDO();
            chatDO.setType(chatDTO.getType());
            chatId = chatDAO.save(chatDO);
        }

        // 创建聊天成员关系
        ChatMemberDO chatMemberDO = new ChatMemberDO();
        chatMemberDO.setType(chatDTO.getType());
        chatMemberDO.setChatId(chatId);
        chatMemberDO.setMemberId(chatDTO.getMemberId());
        chatMemberDO.setPeerId(chatDTO.getPeerId());
        chatMemberDO.setAvatar(chatDTO.getAvatar());
        chatMemberDO.setNickname(chatDTO.getNickname());
        chatMemberDO.setActiveTime(LocalDateTime.now());
        chatMemberDAO.save(chatMemberDO);

        return chatId;
    }

    @Transactional
    @Override
    public Long save(ChatDTO chatDTO, Long chatId) {
        log.info("创建会话并加入,chatDTO:{} chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        Optional<ChatMemberDO> optional;
        boolean isC2C = Objects.equals(chatDTO.getType(), Constants.CHAT_TYPE_C2C);

        // 根据聊天类型获取会话信息
        if (isC2C) {
            return saveC2c(chatDTO, chatId);

        } else {
            return saveC2g(chatDTO, chatId);
        }
    }

    @Override
    @Transactional
    public CreateChatResponse createChat(CreateChatRequest request) {

        ChatDTO chatDTO = new ChatDTO();
        chatDTO.setMemberId(request.getOperatorNo());
        chatDTO.setType(request.getChatType());
        chatDTO.setPeerId(request.getPeerOperatorNo());
        log.debug("get operator info from dubbo ,peerOperatorNo:{}", request.getPeerOperatorNo());
        OperatorInfoRespDTO operatorInfoRespDTO = userOperatorFacade
                .getOperatorInfoByOperatorNo(request.getPeerOperatorNo());
        chatDTO.setNickname(operatorInfoRespDTO.getNickName());
        chatDTO.setAvatar(operatorInfoRespDTO.getHeadURL());

        Long chatId = save(chatDTO, null);

        ChatDTO chatDTO2 = new ChatDTO();
        chatDTO2.setMemberId(request.getPeerOperatorNo());
        chatDTO2.setType(request.getChatType());
        chatDTO2.setPeerId(request.getOperatorNo());
        log.debug("get operator info from dubbo ,getOperatorNo:{}", request.getPeerOperatorNo());
        OperatorInfoRespDTO operatorInfoRespDTO2 = userOperatorFacade
                .getOperatorInfoByOperatorNo(request.getOperatorNo());
        chatDTO2.setNickname(operatorInfoRespDTO2.getNickName());
        chatDTO2.setAvatar(operatorInfoRespDTO2.getHeadURL());

        save(chatDTO2, chatId);
        LastMessageVO lastMessageVO = new LastMessageVO();
        CreateChatResponse createChatResponse = new CreateChatResponse();

        if (Objects.equals(request.getChatType(), Constants.CHAT_TYPE_C2C)) {
            LastC2cMessageDTO lastC2cMessageDTO = c2cApi.getLastMessageByChatIdAndOperatorNo(chatId,
                    request.getOperatorNo(), request.getDeviceId());
            if (ObjectUtil.isNotNull(lastC2cMessageDTO)) {
                BeanUtils.copyProperties(lastC2cMessageDTO, lastMessageVO);
                createChatResponse.setLastMessage(lastMessageVO);
                if (ObjectUtil.isNotNull(lastC2cMessageDTO.getUnReadCount())) {
                    createChatResponse.setUnReadCount(lastC2cMessageDTO.getUnReadCount());
                }
            }
        } else {
            LastC2gMessageDTO lastC2gMessageDTO = c2gApi.getLastMessageByChatIdAndOperatorNo(chatId,
                    request.getOperatorNo(), request.getDeviceId());
            if (ObjectUtil.isNotNull(lastC2gMessageDTO)) {
                BeanUtils.copyProperties(lastC2gMessageDTO, lastMessageVO);
                createChatResponse.setLastMessage(lastMessageVO);
                if (ObjectUtil.isNotNull(lastC2gMessageDTO.getUnReadCount())) {
                    createChatResponse.setUnReadCount(lastC2gMessageDTO.getUnReadCount());
                }
            }
        }

        createChatResponse.setChatType(chatDTO.getType());
        createChatResponse.setOperatorNo(chatDTO.getPeerId());
        createChatResponse.setNickname(chatDTO.getNickname());
        createChatResponse.setAvatar(operatorInfoRespDTO.getHeadURL());
        createChatResponse.setChatId(chatId);

        return createChatResponse;
    }

    @Transactional
    @Override
    public void deleteByChatId(Long chatId) {

        chatMemberService.deleteByChatId(chatId);

        if (chatMemberService.countByChatId(chatId) == 0) {
            chatDAO.remove(chatId);
        }

    }

    @Override
    @Transactional
    public void delete(Long chatId, String operatorNo) {

        QueryWrapper<Object> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("member_id", operatorNo);
        deleteWrapper.eq("chat_id", chatId);
        chatMemberDAO.remove(deleteWrapper);

        if (chatMemberService.countByChatId(chatId) <= 0) {
            chatDAO.remove(chatId);
        }

    }

    @Override
    public Long getChatIdByGroupId(Long groupId) {
        return chatMemberService.getChatIdByGroupId(groupId);
    }
}
