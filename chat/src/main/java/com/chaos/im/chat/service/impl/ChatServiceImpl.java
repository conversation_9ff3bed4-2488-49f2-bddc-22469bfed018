package com.chaos.im.chat.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaos.im.c2c.api.C2cApi;
import com.chaos.im.c2c.api.domain.LastC2cMessageDTO;
import com.chaos.im.c2g.group.api.GroupApi;
import com.chaos.im.c2g.group.api.domain.LastC2gMessageDTO;
import com.chaos.im.chat.dao.ChatDAO;
import com.chaos.im.chat.dao.ChatMemberDAO;
import com.chaos.im.chat.domain.*;
import com.chaos.im.chat.service.ChatMemberService;
import com.chaos.im.chat.service.ChatService;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.resp.OperatorInfoRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatServiceImpl implements ChatService {

    @Autowired
    private ChatDAO chatDAO;

    @Autowired
    private ChatMemberDAO chatMemberDAO;

    @DubboReference(version = "1.0.0")
    private UserOperatorFacade userOperatorFacade;

    @Autowired
    private ChatMemberService chatMemberService;

    @DubboReference(version = "1.0.0")
    private C2cApi c2cApi;

    @DubboReference(version = "1.0.0")
    private GroupApi c2gApi;

    @Override
    public IPage<ChatMemberDTO> pageChat(ChatListRequest chatListRequest) {

        QueryWrapper<ChatMemberDO> queryWrapper = new QueryWrapper<ChatMemberDO>();
        queryWrapper.eq("member_id", chatListRequest.getOperatorNo());
        if (ObjectUtil.isNotNull(chatListRequest.getChatType())) {
            queryWrapper.eq("type", chatListRequest.getChatType());
        }
        queryWrapper.orderByDesc("gmt_create");

        IPage<ChatMemberDO> page = chatMemberService.pageByMemberId(chatListRequest.getOperatorNo(),
                chatListRequest.getCurrent(), chatListRequest.getPageSize());

        List<ChatMemberDTO> chatMemberDTOList = page.getRecords().stream().map(it -> {
            ChatMemberDTO chatMemberDTO = it.clone(ChatMemberDTO.class);
            chatMemberDTO.setId(it.getChatId());
            LastMessageDTO lastMessageDTO = new LastMessageDTO();
            if (Objects.equals(it.getType(), Constants.CHAT_TYPE_C2C)) {
                LastC2cMessageDTO lastC2cMessageDTO = c2cApi.getLastMessageByChatIdAndOperatorNo(it.getChatId(),
                        chatListRequest.getOperatorNo(), chatListRequest.getDeviceId());
                if (ObjectUtil.isNotNull(lastC2cMessageDTO)) {
                    BeanUtils.copyProperties(lastC2cMessageDTO, lastMessageDTO);
                    chatMemberDTO.setLastMessage(lastMessageDTO);
                }
            } else {
                LastC2gMessageDTO lastC2gMessageDTO = c2gApi.getLastMessageByChatIdAndOperatorNo(it.getChatId(),
                        chatListRequest.getOperatorNo(), chatListRequest.getDeviceId());
                if (ObjectUtil.isNotNull(lastC2gMessageDTO)) {
                    BeanUtils.copyProperties(lastC2gMessageDTO, lastMessageDTO);
                    chatMemberDTO.setLastMessage(lastMessageDTO);
                }
            }
            return chatMemberDTO;
        }).collect(Collectors.toList());

        Page<ChatMemberDTO> resPage = new Page<>(page.getCurrent(), page.getSize());
        resPage.setTotal(page.getTotal());
        resPage.setRecords(chatMemberDTOList);

        return resPage;
    }

    public Long saveC2c(ChatDTO chatDTO, Long chatId) {
        log.info("创建会话并加入,chatDTO:{} chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        Optional<ChatMemberDO> optional;

        // 根据聊天类型获取会话信息
        log.info("单聊会话 chatDTO:{},chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        optional = chatMemberDAO.get(chatDTO.getType(), chatDTO.getMemberId(), chatDTO.getPeerId());

        if(optional.isPresent()){
            return optional.get().getChatId();
        }
        //单聊，且不存在会话，则创建会话
        if (chatId == null) {
            // 创建新的聊天会话
            ChatDO chatDO = new ChatDO();
            chatDO.setType(chatDTO.getType());
            chatId = chatDAO.save(chatDO);
        }

        // 创建聊天成员关系
        ChatMemberDO chatMemberDO = new ChatMemberDO();
        chatMemberDO.setType(chatDTO.getType());
        chatMemberDO.setChatId(chatId);
        chatMemberDO.setMemberId(chatDTO.getMemberId());
        chatMemberDO.setPeerId(chatDTO.getPeerId());
        chatMemberDO.setAvatar(chatDTO.getAvatar());
        chatMemberDO.setNickname(chatDTO.getNickname());
        chatMemberDO.setActiveTime(LocalDateTime.now());
        chatMemberDAO.save(chatMemberDO);

        return chatId;
    }

    public Long saveC2g(ChatDTO chatDTO, Long chatId) {
        log.info("创建会话并加入,chatDTO:{} chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        Optional<ChatMemberDO> optional;
        log.info("创建群聊会话 chatDTO:{},chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        optional = chatMemberDAO.get(chatDTO.getType(), chatDTO.getPeerId());

        if (optional.isPresent()) {
            chatId = optional.get().getChatId();
        }

        //群聊，且不存在会话,则创建会话
        if (chatId == null) {
            // 创建新的聊天会话
            ChatDO chatDO = new ChatDO();
            chatDO.setType(chatDTO.getType());
            chatId = chatDAO.save(chatDO);
        }

        // 创建聊天成员关系
        ChatMemberDO chatMemberDO = new ChatMemberDO();
        chatMemberDO.setType(chatDTO.getType());
        chatMemberDO.setChatId(chatId);
        chatMemberDO.setMemberId(chatDTO.getMemberId());
        chatMemberDO.setPeerId(chatDTO.getPeerId());
        chatMemberDO.setAvatar(chatDTO.getAvatar());
        chatMemberDO.setNickname(chatDTO.getNickname());
        chatMemberDO.setActiveTime(LocalDateTime.now());
        chatMemberDAO.save(chatMemberDO);

        return chatId;
    }

    @Transactional
    @Override
    public Long save(ChatDTO chatDTO, Long chatId) {
        log.info("创建会话并加入,chatDTO:{} chatId:{}", JSONUtil.toJsonStr(chatDTO), chatId);
        Optional<ChatMemberDO> optional;
        boolean isC2C = Objects.equals(chatDTO.getType(), Constants.CHAT_TYPE_C2C);

        // 根据聊天类型获取会话信息
        if (isC2C) {
            return saveC2c(chatDTO, chatId);

        } else {
            return saveC2g(chatDTO, chatId);
        }
    }

    @Override
    @Transactional
    public CreateChatResponse createChat(CreateChatRequest request) {

        ChatDTO chatDTO = new ChatDTO();
        chatDTO.setMemberId(request.getOperatorNo());
        chatDTO.setType(request.getChatType());
        chatDTO.setPeerId(request.getPeerOperatorNo());
        log.debug("get operator info from dubbo ,peerOperatorNo:{}", request.getPeerOperatorNo());
        OperatorInfoRespDTO operatorInfoRespDTO = userOperatorFacade
                .getOperatorInfoByOperatorNo(request.getPeerOperatorNo());
        chatDTO.setNickname(operatorInfoRespDTO.getNickName());
        chatDTO.setAvatar(operatorInfoRespDTO.getHeadURL());

        Long chatId = save(chatDTO, null);

        ChatDTO chatDTO2 = new ChatDTO();
        chatDTO2.setMemberId(request.getPeerOperatorNo());
        chatDTO2.setType(request.getChatType());
        chatDTO2.setPeerId(request.getOperatorNo());
        log.debug("get operator info from dubbo ,getOperatorNo:{}", request.getPeerOperatorNo());
        OperatorInfoRespDTO operatorInfoRespDTO2 = userOperatorFacade
                .getOperatorInfoByOperatorNo(request.getOperatorNo());
        chatDTO2.setNickname(operatorInfoRespDTO2.getNickName());
        chatDTO2.setAvatar(operatorInfoRespDTO2.getHeadURL());

        save(chatDTO2, chatId);
        LastMessageVO lastMessageVO = new LastMessageVO();
        CreateChatResponse createChatResponse = new CreateChatResponse();

        if (Objects.equals(request.getChatType(), Constants.CHAT_TYPE_C2C)) {
            LastC2cMessageDTO lastC2cMessageDTO = c2cApi.getLastMessageByChatIdAndOperatorNo(chatId,
                    request.getOperatorNo(), request.getDeviceId());
            if (ObjectUtil.isNotNull(lastC2cMessageDTO)) {
                BeanUtils.copyProperties(lastC2cMessageDTO, lastMessageVO);
                createChatResponse.setLastMessage(lastMessageVO);
                if (ObjectUtil.isNotNull(lastC2cMessageDTO.getUnReadCount())) {
                    createChatResponse.setUnReadCount(lastC2cMessageDTO.getUnReadCount());
                }
            }
        } else {
            LastC2gMessageDTO lastC2gMessageDTO = c2gApi.getLastMessageByChatIdAndOperatorNo(chatId,
                    request.getOperatorNo(), request.getDeviceId());
            if (ObjectUtil.isNotNull(lastC2gMessageDTO)) {
                BeanUtils.copyProperties(lastC2gMessageDTO, lastMessageVO);
                createChatResponse.setLastMessage(lastMessageVO);
                if (ObjectUtil.isNotNull(lastC2gMessageDTO.getUnReadCount())) {
                    createChatResponse.setUnReadCount(lastC2gMessageDTO.getUnReadCount());
                }
            }
        }

        createChatResponse.setChatType(chatDTO.getType());
        createChatResponse.setOperatorNo(chatDTO.getPeerId());
        createChatResponse.setNickname(chatDTO.getNickname());
        createChatResponse.setAvatar(operatorInfoRespDTO.getHeadURL());
        createChatResponse.setChatId(chatId);

        return createChatResponse;
    }

    @Transactional
    @Override
    public void deleteByChatId(Long chatId) {

        chatMemberService.deleteByChatId(chatId);

        if (chatMemberService.countByChatId(chatId) == 0) {
            chatDAO.remove(chatId);
        }

    }

    @Override
    @Transactional
    public void delete(Long chatId, String operatorNo) {

        QueryWrapper<Object> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("member_id", operatorNo);
        deleteWrapper.eq("chat_id", chatId);
        chatMemberDAO.remove(deleteWrapper);

        if (chatMemberService.countByChatId(chatId) <= 0) {
            chatDAO.remove(chatId);
        }

    }

    @Override
    public Long getChatIdByGroupId(Long groupId) {
        return chatMemberService.getChatIdByGroupId(groupId);
    }
}
