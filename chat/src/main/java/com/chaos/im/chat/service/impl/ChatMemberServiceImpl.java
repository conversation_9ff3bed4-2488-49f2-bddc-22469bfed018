package com.chaos.im.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaos.im.chat.dao.ChatMemberDAO;
import com.chaos.im.chat.domain.ChatMemberDO;
import com.chaos.im.chat.mapper.ChatMemberMapper;
import com.chaos.im.chat.service.ChatMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ChatMemberServiceImpl extends ServiceImpl<ChatMemberMapper, ChatMemberDO> implements ChatMemberService {

    @Autowired
    private ChatMemberDAO chatMemberDAO;

    @Override
    public String getPeerIdByChatId(Long chatId) {

        Optional<ChatMemberDO> optional = chatMemberDAO.getOneByChatId(chatId);

        return optional.map(ChatMemberDO::getPeerId).orElse(null);

    }

    @Override
    public int countByChatId(Long chatId) {

        QueryWrapper<ChatMemberDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("chat_id", chatId);
        return count(queryWrapper);

    }

    @Override
    public void deleteByChatId(Long chatId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("chat_id", chatId);
        remove(queryWrapper);
    }

    @Override
    public void deleteChatMember(Integer type, Long groupId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("peer_id", groupId);
        queryWrapper.eq("type", type);
        remove(queryWrapper);
    }

    @Override
    public void deleteChatMember(Integer type, Long chatId, String memberId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("peer_id", chatId);
        queryWrapper.eq("member_id", memberId);
        queryWrapper.eq("type", type);
        remove(queryWrapper);
    }

    @Override
    public void updateActiveTimeByChatId(Long chatId, String operatorNo, LocalDateTime activeTime) {
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("chat_id",chatId);
        updateWrapper.eq("member_id",operatorNo);

        updateWrapper.set("active_time",activeTime);



        update(updateWrapper);
    }

    @Override
    public IPage<ChatMemberDO> pageByMemberId(String memberId, Integer current, Integer pageSize) {

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("member_id", memberId);
        queryWrapper.orderByDesc("gmt_create");

        Page<ChatMemberDO> page = new Page<>(current, pageSize);

       IPage<ChatMemberDO>  res = this.baseMapper.selectPage(page, queryWrapper);

        return res;
    }

    @Override
    public Long getChatIdByGroupId(Long groupId) {
        LambdaQueryWrapper<ChatMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMemberDO::getPeerId, groupId);
        queryWrapper.eq(ChatMemberDO::getType, 2);
        List<ChatMemberDO> chatMemberDOs = list(queryWrapper);
        if(!chatMemberDOs.isEmpty()){
           return chatMemberDOs.get(0).getChatId();
        }

        return null;

    }


}
