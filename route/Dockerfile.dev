# 开发环境热启动Dockerfile
# 使用阿里云镜像源
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/openjdk:8-jdk-alpine-linuxarm64

RUN apk add --no-cache curl wget bash tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

WORKDIR /app

RUN addgroup -g 1000 appuser && \
    adduser -D -s /bin/sh -u 1000 -G appuser appuser && \
    mkdir -p /app/target/classes /app/logs /app/lib && \
    chown -R appuser:appuser /app

COPY dev-entrypoint.sh /app/dev-entrypoint.sh
RUN chmod +x /app/dev-entrypoint.sh

USER appuser
EXPOSE 9097 9670 20990 5009

ENV JAVA_OPTS="-Xms256m -Xmx512m \
    -XX:+UseG1GC \
    -Dspring.devtools.restart.enabled=true \
    -Dspring.devtools.livereload.enabled=true \
    -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6007"

ENTRYPOINT ["/app/dev-entrypoint.sh"]
