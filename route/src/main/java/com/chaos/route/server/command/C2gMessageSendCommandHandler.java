package com.chaos.route.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.im.constant.CommandType;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.constant.MessageType;
import com.chaos.keep.alive.common.im.domain.MessageAckJsonRequest;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.protobuf.C2gMessageAckRequest;
import com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest;
import com.chaos.keep.alive.common.protobuf.Command;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class C2gMessageSendCommandHandler implements ServerCommandHandler {

    private final KafkaTemplate<String, String> kafkaTemplate;

    @Value("#{'topic-c2g-message-send-'.concat('${spring.profiles.active}')}")
    private String TOPIC_C2G_MESSAGE_SEND;

    @Value("#{'topic-c2g-message-ack-'.concat('${spring.profiles.active}')}")
    private String TOPIC_C2G_MESSAGE_ACK;

    private final Executor taskExecutor;

    private final RedisTemplate<String, Long> redisTemplate;


    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        taskExecutor.execute(() -> {
            try {
                if (command.getMessageType() == MessageType.MESSAGE_TYPE_CLIENT_ACK) {
                    C2gMessageAckRequest body = C2gMessageAckRequest.parseFrom(command.getBody());
                    MessageAckJsonRequest request = new MessageAckJsonRequest();
                    request.setDeviceId(body.getDeviceId());
                    request.setOperatorNo(body.getOperatorNo());
                    request.setMessageId(body.getMessageId());
                    request.setChatType(body.getChatType());
                    request.setChatId(body.getChatId());
                    request.setSequence(body.getSequence());

                    kafkaTemplate.send(TOPIC_C2G_MESSAGE_ACK, String.valueOf(request.getChatId()), JSONUtil.toJsonStr(request));
                    log.info(JSONUtil.toJsonStr(request));

                } else {
                    C2gMessageSendRequest body = C2gMessageSendRequest.parseFrom(command.getBody());

                    MessageSendJsonRequest request = new MessageSendJsonRequest();
                    request.setMessageId(body.getMessageId());
                    request.setFromOperatorNo(body.getFromOperatorNo());
                    request.setChatId(body.getChatId());
                    request.setChatType(Constants.CHAT_TYPE_C2G);
                    request.setContent(body.getContent());
                    request.setCategory(body.getCategory());
                    if (CommandType.COMMAND_IM_C2G_MESSAGE_SEND == command.getBizType()) {
                        // 使用 redis 获取当前服务的 sequence
                        Long chatId =  request.getChatId();
                        Optional<Long> sequenceOptional = Optional.ofNullable(redisTemplate.boundValueOps(Constants.REDIS_SEQ_KEY + "::" + chatId).increment());
                        // 群聊服务端设置sequence
                        request.setSequence(sequenceOptional.orElse(1L));
                        kafkaTemplate.send(TOPIC_C2G_MESSAGE_SEND, String.valueOf(request.getChatId()), JSONUtil.toJsonStr(request));
                        log.info(JSONUtil.toJsonStr(request));
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_IM_C2G_MESSAGE_SEND;
    }
}
