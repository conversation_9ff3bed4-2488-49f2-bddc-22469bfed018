server:
  port: 9097

spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: gonow-ka-route
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms
  kafka:
    producer:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka:9092}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka:9092}
      group-id: ka-route
      auto-offset-reset: latest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      type: batch
      ack-mode: manual_immediate

ka:
  serverId: ${ROUTE_SERVER_ID:keep-alive-route}
  port: ${ROUTE_TCP_PORT:9670}
  authenticate: false
  heartbeat:
    readTimeout: 30000
  zk:
    enable: true
    retry: 3
    intervalTime: 1000
    zkServer: ${ZOOKEEPER_HOST:zookeeper}:${ZOOKEEPER_PORT:2181}

dubbo:
  application:
    name: driver-trajectory
  registry:
    address: zookeeper://${ZOOKEEPER_HOST:zookeeper}:${ZOOKEEPER_PORT:2181}
  protocol:
    port: 20990
  provider:
    group: chaos
  consumer:
    group: chaos

# 日志配置
logging:
  level:
    com.chaos.route: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/route.log

# RocketMQ配置
rocketmq:
  maxReconsumeTimes: 10
  name-server: ${ROCKETMQ_NAME_SERVER:rocketmq:9876}
  consumer:
    group: im-route-consumer
  producer:
    group: im-route-producer

# 监控端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
